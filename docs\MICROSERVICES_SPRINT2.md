# BiblioAI - Architecture Microservices Sprint 2

## Tableau des Microservices

| Microservice | Port | Responsabilité | Endpoints Principaux | Base de Données |
|--------------|------|----------------|---------------------|-----------------|
| **Auth Service** | 5001 | Authentification et autorisation | `/register`, `/login`, `/verify`, `/profile` | MySQL (biblioai_auth) |
| **Document Service** | 5002 | Gestion des documents | `/documents/*`, `/generate-barcode`, `/categories` | MySQL (biblioai_documents) |
| **Classification Service** | 5003 | Classification automatique des documents | `/classify`, `/categories`, `/subjects`, `/rules` | MySQL (biblioai_classification) |
| **Search Service** | 5004 | Recherche avancée et indexation | `/search`, `/suggestions`, `/index`, `/reindex` | MySQL (biblioai_search) |
| **API Gateway** | 5000 | Point d'entrée unique, routage | `/api/*` | - |

## Architecture

```
Frontend (React) → API Gateway (5000) → Microservices
                                      ├── Auth Service (5001)
                                      ├── Document Service (5002)
                                      ├── Classification Service (5003)
                                      └── Search Service (5004)
                                              ↓
                                      MySQL Database
```

## Instructions de Démarrage

### Prérequis
1. **Python 3.11+** installé
2. **MySQL** en cours d'exécution
3. **Bases de données créées** :
   ```sql
   CREATE DATABASE biblioai_auth;
   CREATE DATABASE biblioai_documents;
   CREATE DATABASE biblioai_classification;
   CREATE DATABASE biblioai_search;
   ```

### Démarrage Manuel (Recommandé)

#### 1. Auth Service (Port 5001)
```bash
cd microservices/auth-service
python -m venv venv
venv\Scripts\activate  # Windows
# ou source venv/bin/activate  # Linux/Mac
pip install -r requirements.txt
python app.py
```

#### 2. Document Service (Port 5002)
```bash
cd microservices/document-service
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
python app.py
```

#### 3. Classification Service (Port 5003)
```bash
cd microservices/classification-service
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
python app.py
```

#### 4. Search Service (Port 5004)
```bash
cd microservices/search-service
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
python app.py
```

#### 5. API Gateway (Port 5000)
```bash
cd microservices/api-gateway
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
python app.py
```

### Démarrage avec Scripts Batch (Windows)
```bash
# Dans des terminaux séparés :
cd microservices/auth-service && start.bat
cd microservices/document-service && start.bat
cd microservices/classification-service && start.bat
cd microservices/search-service && start.bat
cd microservices/api-gateway && start.bat
```

### Vérification des Services
```bash
cd microservices
python check_services.py
```

## Fonctionnalités des Microservices

### 1. Auth Service (5001)
- **Inscription** : `POST /register`
- **Connexion** : `POST /login`
- **Vérification token** : `GET /verify`
- **Profil utilisateur** : `GET /profile`
- **Liste utilisateurs** : `GET /users` (managers seulement)

### 2. Document Service (5002)
- **CRUD documents** : `GET/POST/PUT/DELETE /documents`
- **Génération code-barres** : `GET /generate-barcode`
- **Catégories** : `GET /categories`
- **Sujets** : `GET /subjects`

### 3. Classification Service (5003)
- **Classification automatique** : `POST /classify`
- **Gestion catégories** : `GET/POST /categories`
- **Gestion sujets** : `GET/POST /subjects`
- **Règles de classification** : `GET/POST/PUT/DELETE /rules`

### 4. Search Service (5004)
- **Recherche avancée** : `GET /search`
- **Suggestions** : `GET /suggestions`
- **Indexation** : `POST /index`
- **Réindexation** : `POST /reindex`
- **Statistiques** : `GET /stats`

### 5. API Gateway (5000)
- **Routage unifié** : `/api/*`
- **Création document enrichie** : `POST /api/documents/enhanced`
- **Vérification santé** : `GET /health`
- **Découverte services** : `GET /api/services`

## Workflow Complet

### 1. Création de Document avec Auto-Classification
```bash
POST /api/documents/enhanced
```
1. **Classification automatique** via Classification Service
2. **Création document** via Document Service
3. **Indexation automatique** via Search Service

### 2. Recherche Intelligente
```bash
GET /api/search?q=terme&type=book&category=science
```
1. **Recherche** dans l'index via Search Service
2. **Récupération détails** via Document Service
3. **Filtrage disponibilité** si demandé

### 3. Authentification Centralisée
```bash
POST /api/auth/login
```
1. **Vérification credentials** via Auth Service
2. **Génération JWT** avec rôles
3. **Validation token** pour chaque requête

## Avantages de l'Architecture Microservices

### ✅ Séparation des Responsabilités
- Chaque service a une responsabilité unique
- Développement et maintenance indépendants
- Équipes spécialisées par domaine

### ✅ Scalabilité
- Mise à l'échelle indépendante par service
- Optimisation des ressources selon les besoins
- Déploiement granulaire

### ✅ Résilience
- Isolation des pannes
- Dégradation gracieuse
- Redondance possible

### ✅ Flexibilité Technologique
- Technologies différentes par service
- Évolution indépendante
- Intégration facilitée

## Configuration Frontend

Pour utiliser l'architecture microservices, mettez à jour le frontend :

```javascript
// biblioai-frontend/src/services/authService.js
const API_URL = 'http://localhost:5000/api/auth';

// biblioai-frontend/src/services/documentService.js  
const API_URL = 'http://localhost:5000/api';
```

## Tests et Validation

### Test Complet
```bash
cd microservices
python test_microservices.py
```

### Test Individuel des Services
```bash
# Auth Service
curl http://localhost:5001/health

# Document Service  
curl http://localhost:5005/health

# Classification Service
curl http://localhost:5003/health

# Search Service
curl http://localhost:5004/health

# API Gateway
curl http://localhost:5000/health
```

## Monitoring et Logs

Chaque service affiche ses logs dans son terminal :
- **Requêtes entrantes**
- **Erreurs et exceptions**
- **État de santé**
- **Connexions base de données**

## Prochaines Étapes

### Sprint 3 - Améliorations Microservices
1. **Service de Notification** (port 5005)
2. **Service de Réservation** (port 5006)
3. **Service de Rapports** (port 5007)
4. **Load Balancer** pour haute disponibilité
5. **Circuit Breaker** pour la résilience
6. **Monitoring centralisé** avec métriques

### Optimisations Possibles
- **Cache Redis** pour les sessions
- **Message Queue** pour communication asynchrone
- **Service Discovery** automatique
- **Configuration centralisée**
- **Logging centralisé** avec ELK Stack

## Dépannage

### Problèmes Courants

1. **Service ne démarre pas**
   - Vérifier que le port n'est pas utilisé
   - Vérifier les dépendances Python
   - Vérifier la connexion MySQL

2. **Erreur de connexion entre services**
   - Vérifier que tous les services sont démarrés
   - Vérifier les URLs de configuration
   - Vérifier les tokens d'authentification

3. **Erreur base de données**
   - Vérifier que MySQL est démarré
   - Vérifier que les bases de données existent
   - Vérifier les permissions utilisateur

### Commandes Utiles
```bash
# Vérifier les ports utilisés
netstat -an | findstr :500

# Arrêter tous les processus Python
taskkill /f /im python.exe

# Redémarrer MySQL
net stop mysql
net start mysql
```

## Conclusion

L'architecture microservices de BiblioAI offre :
- **Modularité** et **maintenabilité** améliorées
- **Scalabilité** horizontale et verticale
- **Développement** parallèle et indépendant
- **Déploiement** granulaire et flexible
- **Résilience** et **tolérance aux pannes**

Cette architecture prépare le système pour une croissance future et facilite l'ajout de nouvelles fonctionnalités.
