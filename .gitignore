# dependencies
/biblioai-frontend/node_modules
/biblioai-backend/venv
/biblioai-backend/__pycache__
/biblioai-backend/*/__pycache__
/biblioai-backend/*/*/__pycache__
/biblioai-backend/*/*/*/__pycache__

/microservices/auth-service/venv
/microservices/auth-service/__pycache__
/microservices/document-service/venv
/microservices/document-service/__pycache__
/microservices/classification-service/venv
/microservices/classification-service/__pycache__
/microservices/search-service/venv
/microservices/search-service/__pycache__
/microservices/api-gateway/venv
/microservices/api-gateway/__pycache__

# testing
/biblioai-frontend/coverage

# production
/biblioai-frontend/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.env

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE files
.idea/
.vscode/
*.swp
*.swo
