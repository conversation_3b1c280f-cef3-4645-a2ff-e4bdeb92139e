# BiblioAI - Sprint 3: Member Services Microservices Architecture

## Overview

Sprint 3 implements comprehensive member services including book reservations, borrowing/return functionality, member dashboard, and notification system using microservices architecture.

## Microservices Architecture - Sprint 3

| Microservice | Port | Responsabilité | Endpoints Principaux | Base de Données |
|--------------|------|----------------|---------------------|-----------------|
| **Auth Service** | 5001 | Authentification et autorisation | `/register`, `/login`, `/verify`, `/profile` | MySQL (biblioai_auth) |
| **Document Service** | 5005 | Gestion des documents | `/documents/*`, `/generate-barcode`, `/categories` | MySQL (biblioai_documents) |
| **Classification Service** | 5003 | Classification automatique des documents | `/classify`, `/categories`, `/subjects`, `/rules` | MySQL (biblioai_classification) |
| **Search Service** | 5004 | Recherche avancée et indexation | `/search`, `/suggestions`, `/index`, `/reindex` | MySQL (biblioai_search) |
| **Member Service** | 5006 | Gestion des membres, réservations, emprunts | `/reservations`, `/borrowings`, `/history`, `/dashboard` | MySQL (biblioai_members) |
| **Notification Service** | 5007 | Système de notifications | `/notifications`, `/notifications/{id}/read` | MySQL (biblioai_notifications) |
| **API Gateway** | 5000 | Point d'entrée unique, routage | `/api/*` | - |

## Architecture

```
Frontend (React) → API Gateway (5000) → Microservices
                                      ├── Auth Service (5001)
                                      ├── Document Service (5005)
                                      ├── Classification Service (5003)
                                      ├── Search Service (5004)
                                      ├── Member Service (5006)
                                      └── Notification Service (5007)
                                              ↓
                                      MySQL Database
```

## Instructions de Démarrage

### Prérequis
1. **Python 3.11+** installé
2. **MySQL** en cours d'exécution
3. **Bases de données créées** :
   ```sql
   CREATE DATABASE biblioai_auth;
   CREATE DATABASE biblioai_documents;
   CREATE DATABASE biblioai_classification;
   CREATE DATABASE biblioai_search;
   CREATE DATABASE biblioai_members;
   CREATE DATABASE biblioai_notifications;
   ```

### Démarrage Automatique
```bash
# Exécuter le script de démarrage
start_biblioai_microservices.bat
```

### Démarrage Manuel

#### 1. Auth Service (Port 5001)
```bash
cd microservices/auth-service
python app.py
```

#### 2. Document Service (Port 5005)
```bash
cd microservices/document-service
python app.py
```

#### 3. Classification Service (Port 5003)
```bash
cd microservices/classification-service
python app.py
```

#### 4. Search Service (Port 5004)
```bash
cd microservices/search-service
python app.py
```

#### 5. Member Service (Port 5006)
```bash
cd microservices/member-service
python app.py
```

#### 6. Notification Service (Port 5007)
```bash
cd microservices/notification-service
python app.py
```

#### 7. API Gateway (Port 5000)
```bash
cd microservices/api-gateway
python app.py
```

## Fonctionnalités Sprint 3

### 1. Member Service (5006)

#### Réservations
- **Créer réservation** : `POST /reservations`
- **Lister réservations** : `GET /reservations`
- **Annuler réservation** : `DELETE /reservations/{id}`

#### Emprunts
- **Créer emprunt** : `POST /borrowings`
- **Lister emprunts** : `GET /borrowings`
- **Retourner document** : `PUT /borrowings/{id}/return`
- **Renouveler emprunt** : `PUT /borrowings/{id}/renew`

#### Historique et Dashboard
- **Historique membre** : `GET /history`
- **Résumé dashboard** : `GET /dashboard`

#### Administration (Bibliothécaires/Managers)
- **Tous les emprunts** : `GET /admin/borrowings`
- **Emprunts en retard** : `GET /admin/overdue`

### 2. Notification Service (5007)

#### Notifications
- **Lister notifications** : `GET /notifications`
- **Créer notification** : `POST /notifications`
- **Marquer comme lu** : `PUT /notifications/{id}/read`
- **Marquer tout comme lu** : `PUT /notifications/mark-all-read`
- **Supprimer notification** : `DELETE /notifications/{id}`

#### Types de Notifications
- `reservation_created` - Réservation confirmée
- `borrowing_created` - Emprunt créé
- `document_returned` - Document retourné
- `borrowing_renewed` - Emprunt renouvelé
- `due_reminder` - Rappel d'échéance
- `overdue_notice` - Avis de retard

### 3. API Gateway Extensions

#### Nouvelles Routes Member Service
- `GET/POST /api/members/reservations`
- `DELETE /api/members/reservations/{id}`
- `GET/POST /api/members/borrowings`
- `PUT /api/members/borrowings/{id}/return`
- `PUT /api/members/borrowings/{id}/renew`
- `GET /api/members/history`
- `GET /api/members/dashboard`
- `GET /api/members/admin/borrowings`
- `GET /api/members/admin/overdue`

#### Nouvelles Routes Notification Service
- `GET/POST /api/notifications`
- `PUT /api/notifications/{id}/read`
- `PUT /api/notifications/mark-all-read`
- `DELETE /api/notifications/{id}`

## Base de Données

### Member Service (biblioai_members)

#### Table: reservations
```sql
- id (INT, PRIMARY KEY)
- user_id (INT, NOT NULL)
- document_id (INT, NOT NULL)
- status (VARCHAR(20), DEFAULT 'active')
- reservation_date (DATETIME)
- expiry_date (DATETIME)
- priority (INT, DEFAULT 1)
- notes (TEXT)
- created_at (DATETIME)
- updated_at (DATETIME)
```

#### Table: borrowings
```sql
- id (INT, PRIMARY KEY)
- user_id (INT, NOT NULL)
- document_id (INT, NOT NULL)
- status (VARCHAR(20), DEFAULT 'active')
- borrow_date (DATETIME)
- due_date (DATETIME)
- return_date (DATETIME)
- renewal_count (INT, DEFAULT 0)
- max_renewals (INT, DEFAULT 2)
- fine_amount (DECIMAL(10,2), DEFAULT 0.00)
- notes (TEXT)
- created_at (DATETIME)
- updated_at (DATETIME)
```

#### Table: member_history
```sql
- id (INT, PRIMARY KEY)
- user_id (INT, NOT NULL)
- document_id (INT, NOT NULL)
- action (VARCHAR(50), NOT NULL)
- action_date (DATETIME)
- details (TEXT)
- created_at (DATETIME)
```

### Notification Service (biblioai_notifications)

#### Table: notifications
```sql
- id (INT, PRIMARY KEY)
- user_id (INT, NOT NULL)
- type (VARCHAR(50), NOT NULL)
- title (VARCHAR(255), NOT NULL)
- message (TEXT, NOT NULL)
- document_id (INT)
- is_read (BOOLEAN, DEFAULT FALSE)
- priority (VARCHAR(20), DEFAULT 'normal')
- scheduled_for (DATETIME)
- sent_at (DATETIME)
- created_at (DATETIME)
- updated_at (DATETIME)
```

#### Table: notification_templates
```sql
- id (INT, PRIMARY KEY)
- type (VARCHAR(50), UNIQUE, NOT NULL)
- title_template (VARCHAR(255), NOT NULL)
- message_template (TEXT, NOT NULL)
- priority (VARCHAR(20), DEFAULT 'normal')
- is_active (BOOLEAN, DEFAULT TRUE)
- created_at (DATETIME)
- updated_at (DATETIME)
```

## Frontend Components

### Services
- `memberService.js` - API calls pour les services membres
- `notificationService.js` - API calls pour les notifications

### Components
- `ReservationList.js` - Liste des réservations
- `BorrowingList.js` - Liste des emprunts
- `NotificationPanel.js` - Panneau de notifications

### Pages
- `MemberDashboard.js` - Dashboard membre amélioré avec onglets

### Styles
- `MemberServices.css` - Styles pour les services membres

## Workflow Complet

### 1. Réservation de Document
```bash
POST /api/members/reservations
```
1. **Vérification disponibilité** via Document Service
2. **Création réservation** dans Member Service
3. **Ajout historique** membre
4. **Notification** via Notification Service

### 2. Emprunt de Document
```bash
POST /api/members/borrowings
```
1. **Vérification disponibilité** via Document Service
2. **Création emprunt** dans Member Service
3. **Mise à jour disponibilité** via Document Service
4. **Fulfillment réservation** si applicable
5. **Notification** via Notification Service

### 3. Retour de Document
```bash
PUT /api/members/borrowings/{id}/return
```
1. **Mise à jour statut emprunt** dans Member Service
2. **Calcul amendes** si retard
3. **Mise à jour disponibilité** via Document Service
4. **Notification** via Notification Service

## Tests et Validation

### Test Complet
```bash
cd microservices
python test_microservices.py
```

### Test Frontend
```bash
cd biblioai-frontend
npm start
```

Accéder à : http://localhost:3000

## Avantages de l'Architecture Sprint 3

### ✅ Services Membres Complets
- Réservations avec système de priorité
- Emprunts avec renouvellements
- Historique détaillé des activités
- Dashboard informatif

### ✅ Système de Notifications
- Notifications en temps réel
- Templates personnalisables
- Différents niveaux de priorité
- Interface utilisateur intuitive

### ✅ Intégration Transparente
- Communication inter-services
- Mise à jour automatique des disponibilités
- Gestion des erreurs gracieuse
- Interface utilisateur unifiée

### ✅ Évolutivité
- Services indépendants
- Base de données séparées
- API RESTful standardisées
- Architecture modulaire

## Configuration Frontend

Pour utiliser les nouveaux services, le frontend est déjà configuré :

```javascript
// biblioai-frontend/src/services/memberService.js
const API_URL = 'http://localhost:5000/api/members';

// biblioai-frontend/src/services/notificationService.js
const API_URL = 'http://localhost:5000/api/notifications';
```

## Conclusion

Sprint 3 complète l'architecture microservices de BiblioAI avec des services membres complets et un système de notifications robuste, offrant une expérience utilisateur riche et une architecture évolutive pour les fonctionnalités futures.
