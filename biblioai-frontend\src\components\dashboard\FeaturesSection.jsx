// src/pages/Home/FeaturesSection.jsx
import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

const FeaturesSection = () => {
  // Theme and Language hooks
  const { getCurrentColors } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();
  const features = [
    {
      id: 1,
      icon: "/images/img_frame_white_a700_45x45.svg",
      title: t('features.searchBook') || "Search book",
      description: t('features.searchBookDesc') || "Effortlessly find your next read with our powerful and intuitive book search."
    },
    {
      id: 2,
      icon: "/images/img_frame_white_a700.svg",
      title: t('features.reviewBook') || "Review book",
      description: t('features.reviewBookDesc') || "Discover insightful critiques and share your thoughts on diverse literary masterpieces effortlessly."
    },
    {
      id: 3,
      icon: "/images/img_frame.svg",
      title: t('features.wishlistBook') || "Wishlist book",
      description: t('features.wishlistBookDesc') || "Curate your literary dreams–wishlist books for future adventures and discoveries."
    }
  ];

  return (
    <section
      id="features"
      className="pt-24 pb-16 px-4 md:px-8 lg:px-20 transition-colors duration-200"
    >
      <div className="mb-12">
        <span
          className="text-lg font-extrabold tracking-wider transition-colors duration-200"
          style={{ color: colors.primary }}
        >
          {t('features.title') || 'FEATURES'}
        </span>
        <h2
          className="text-4xl font-extrabold mt-2 transition-colors duration-200"
          style={{ color: colors.text }}
        >
          🤔• {t('features.subtitle') || 'What You Can Do?'}
        </h2>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-10 mt-10">
        {features.map((feature) => (
          <div key={feature.id} className="flex flex-col items-center">
            <div
              className="w-[102px] h-[102px] rounded-[26px] flex items-center justify-center shadow-[0_30px_60px_rgba(71,74,87,0.25)] transition-colors duration-300"
              style={{
                backgroundColor: colors.primary,
                ':hover': {
                  backgroundColor: colors.primary
                }
              }}
            >
              <img
                src={feature.icon}
                alt={feature.title}
                className="w-[45px] h-[45px]"
              />
            </div>

            <h3
              className="text-2xl font-bold mt-5 transition-colors duration-200"
              style={{ color: colors.text }}
            >
              {feature.title}
            </h3>

            <p
              className="text-base text-center mt-3 leading-relaxed transition-colors duration-200"
              style={{ color: colors.textSecondary }}
            >
              {feature.description}
            </p>
          </div>
        ))}
      </div>
    </section>
  );
};

export default FeaturesSection;