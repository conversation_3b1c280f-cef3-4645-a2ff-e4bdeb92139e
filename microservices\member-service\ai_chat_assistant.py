#!/usr/bin/env python3
"""
AI Chat Assistant for library members
"""

import openai
import requests
import json
import os
from datetime import datetime

class LibraryAIChatAssistant:
    def __init__(self):
        self.openai_api_key = os.environ.get('OPENAI_API_KEY')
        if self.openai_api_key:
            openai.api_key = self.openai_api_key
        
        self.system_prompt = """
        You are a helpful AI assistant for a digital library system. You can help members with:
        
        1. Finding books and resources
        2. Answering questions about library policies
        3. Helping with reservations and borrowings
        4. Providing reading recommendations
        5. Explaining library features
        
        Library Information:
        - Members can borrow books for 14 days
        - Books can be renewed once if no one is waiting
        - Late returns incur a $0.50 per day fine
        - Members can reserve books that are currently borrowed
        - The library has books, periodicals, and digital resources
        
        Always be helpful, friendly, and provide accurate information about library services.
        If you need to perform actions (like searching for books), ask for clarification.
        """
    
    def get_library_context(self, user_id):
        """Get current library context for the user"""
        try:
            context = {
                'user_borrowings': self.get_user_borrowings(user_id),
                'user_reservations': self.get_user_reservations(user_id),
                'available_books_count': self.get_available_books_count(),
                'user_fines': self.get_user_fines(user_id)
            }
            return context
        except Exception as e:
            print(f"Error getting library context: {e}")
            return {}
    
    def get_user_borrowings(self, user_id):
        """Get user's current borrowings"""
        try:
            response = requests.get(f"http://localhost:5006/borrowings?user_id={user_id}")
            if response.status_code == 200:
                return response.json().get('borrowings', [])
            return []
        except:
            return []
    
    def get_user_reservations(self, user_id):
        """Get user's current reservations"""
        try:
            response = requests.get(f"http://localhost:5006/reservations?user_id={user_id}")
            if response.status_code == 200:
                return response.json().get('reservations', [])
            return []
        except:
            return []
    
    def get_available_books_count(self):
        """Get count of available books"""
        try:
            response = requests.get("http://localhost:5005/documents")
            if response.status_code == 200:
                documents = response.json().get('documents', [])
                return len([doc for doc in documents if doc.get('available_copies', 0) > 0])
            return 0
        except:
            return 0
    
    def get_user_fines(self, user_id):
        """Get user's outstanding fines"""
        try:
            # This would be implemented based on your fine tracking system
            return 0.0
        except:
            return 0.0
    
    def search_books(self, query):
        """Search for books in the library"""
        try:
            response = requests.get(f"http://localhost:5004/search?q={query}")
            if response.status_code == 200:
                return response.json().get('results', [])
            return []
        except:
            return []
    
    def chat_with_ai(self, user_id, message, conversation_history=None):
        """Main chat function with AI assistant"""
        if not self.openai_api_key:
            return self.get_fallback_response(message)
        
        try:
            # Get library context
            context = self.get_library_context(user_id)
            
            # Prepare context message
            context_message = f"""
            Current Library Status for User:
            - Active Borrowings: {len(context.get('user_borrowings', []))}
            - Active Reservations: {len(context.get('user_reservations', []))}
            - Available Books in Library: {context.get('available_books_count', 0)}
            - Outstanding Fines: ${context.get('user_fines', 0):.2f}
            
            Recent Borrowings:
            {json.dumps(context.get('user_borrowings', [])[:3], indent=2)}
            """
            
            # Prepare conversation
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "system", "content": context_message}
            ]
            
            # Add conversation history
            if conversation_history:
                messages.extend(conversation_history[-10:])  # Last 10 messages
            
            # Add current message
            messages.append({"role": "user", "content": message})
            
            # Check if user is asking for book search
            if any(keyword in message.lower() for keyword in ['find', 'search', 'book', 'author', 'title']):
                search_results = self.handle_book_search(message)
                if search_results:
                    search_context = f"Search Results: {json.dumps(search_results[:5], indent=2)}"
                    messages.append({"role": "system", "content": search_context})
            
            # Get AI response
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=messages,
                max_tokens=300,
                temperature=0.7
            )
            
            ai_response = response.choices[0].message.content
            
            return {
                'success': True,
                'response': ai_response,
                'context': context,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            print(f"AI chat error: {e}")
            return self.get_fallback_response(message)
    
    def handle_book_search(self, message):
        """Extract search terms and search for books"""
        try:
            # Simple keyword extraction (you could use NLP here)
            search_terms = []
            
            # Look for quoted phrases
            import re
            quoted = re.findall(r'"([^"]*)"', message)
            search_terms.extend(quoted)
            
            # Look for common book-related keywords
            words = message.lower().split()
            book_keywords = ['book', 'author', 'title', 'novel', 'story', 'read']
            
            for i, word in enumerate(words):
                if word in book_keywords and i + 1 < len(words):
                    search_terms.append(words[i + 1])
            
            # Search for books
            if search_terms:
                query = ' '.join(search_terms)
                return self.search_books(query)
            
            return []
            
        except Exception as e:
            print(f"Book search error: {e}")
            return []
    
    def get_fallback_response(self, message):
        """Fallback response when AI is not available"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ['hello', 'hi', 'help']):
            response = """
            Hello! I'm your library assistant. I can help you with:
            
            📚 Finding books and resources
            📋 Checking your borrowings and reservations
            ⏰ Information about due dates and renewals
            💰 Checking fines and fees
            📖 Getting reading recommendations
            
            What would you like to help with today?
            """
        elif any(word in message_lower for word in ['borrow', 'due', 'return']):
            response = """
            📚 Borrowing Information:
            - Books can be borrowed for 14 days
            - You can renew once if no one is waiting
            - Late returns incur $0.50 per day fine
            
            To check your current borrowings, visit your dashboard.
            """
        elif any(word in message_lower for word in ['reserve', 'reservation']):
            response = """
            📋 Reservations:
            - You can reserve books that are currently borrowed
            - You'll be notified when the book becomes available
            - Reservations expire after 7 days
            
            To make a reservation, search for the book and click 'Reserve'.
            """
        elif any(word in message_lower for word in ['fine', 'fee', 'money']):
            response = """
            💰 Fines and Fees:
            - Late returns: $0.50 per day
            - Lost books: Replacement cost + $5 processing fee
            - Damaged books: Assessed based on damage
            
            Check your dashboard for current fine balance.
            """
        else:
            response = """
            I'm here to help with library services! You can ask me about:
            
            • Finding books
            • Borrowing policies
            • Reservations
            • Fines and fees
            • Reading recommendations
            
            What specific question do you have?
            """
        
        return {
            'success': True,
            'response': response,
            'context': {},
            'timestamp': datetime.utcnow().isoformat()
        }

# Integration function for the main app
def chat_with_library_assistant(user_id, message, conversation_history=None):
    """Main function to chat with the library AI assistant"""
    assistant = LibraryAIChatAssistant()
    return assistant.chat_with_ai(user_id, message, conversation_history)
