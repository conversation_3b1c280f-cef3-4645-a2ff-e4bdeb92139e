import React, { useState, useEffect, useRef } from 'react';
import {
  createDocument,
  updateDocument,
  generateBarcode,
  DOCUMENT_TYPES,
  FORMAT_OPTIONS,
  LANGUAGE_OPTIONS,
  COMMON_CATEGORIES,
  COMMON_SUBJECTS
} from '../../services/documentService';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import '../../styles/DocumentForm.css';
// import '../../styles/LibrarianDashboard.css'; // Import for classification styling

const DocumentForm = ({ document: initialDocument, onSave, onCancel }) => {
  // Theme and Language hooks
  const { getCurrentColors } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    document_type: 'book',
    isbn: '',
    barcode: '',
    category: '',
    subject: '',
    keywords: '',
    publisher: '',
    publication_date: '',
    language: 'English',
    pages: '',
    format: '',
    location: '',
    total_copies: 1,
    available_copies: 1,
    // Type-specific fields
    author: '',
    edition: '',
    series: '',
    issn: '',
    volume: '',
    issue: '',
    frequency: '',
    journal: '',
    doi: '',
    director: '',
    duration: '',
    resolution: '',
    // Image field
    image: null,
    image_path: initialDocument?.image_path || ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [generatingBarcode, setGeneratingBarcode] = useState(false);
  
  // GROQ AI Classification States
  const [isClassifying, setIsClassifying] = useState(false);
  const [classificationResult, setClassificationResult] = useState(null);
  
  // Book suggestion states
  const [bookSuggestions, setBookSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const suggestionRef = useRef(null);



  const isEditing = !!initialDocument;

  useEffect(() => {
    if (initialDocument) {
      setFormData({
        ...initialDocument,
        // Ensure numerical values are not strings
        pages: initialDocument.pages ? Number(initialDocument.pages) : '',
        total_copies: initialDocument.total_copies ? Number(initialDocument.total_copies) : 1,
        available_copies: initialDocument.available_copies ? Number(initialDocument.available_copies) : 1,
        duration: initialDocument.duration ? Number(initialDocument.duration) : ''
      });
    }
    
    // Add click outside listener to close suggestions
    const handleClickOutside = (event) => {
      if (suggestionRef.current && !suggestionRef.current.contains(event.target)) {
        setShowSuggestions(false);
      }
    };
    
    // Use window.document to avoid conflict with the 'document' parameter
    window.document.addEventListener('mousedown', handleClickOutside);
    return () => {
      window.document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [initialDocument]);

  // OpenLibrary API integration for book search
  const searchBooks = async (query) => {
    if (!query || query.length < 2) return [];
    
    setIsSearching(true);
    console.log(`SEARCH DEBUG: Searching for book titles matching "${query}"`);
    
    try {
      // Step 1: Search for books by title
      const searchUrl = `https://openlibrary.org/search.json?title=${encodeURIComponent(query)}&limit=5`;
      console.log(`SEARCH DEBUG: Fetching from URL: ${searchUrl}`);
      
      const searchResponse = await fetch(searchUrl);
      if (!searchResponse.ok) {
        console.error(`SEARCH DEBUG: Search request failed with status: ${searchResponse.status}`);
        throw new Error('Failed to search books');
      }
      
      const searchData = await searchResponse.json();
      console.log('SEARCH DEBUG: Search results received:', searchData);
      
      if (!searchData.docs || searchData.docs.length === 0) {
        console.log('SEARCH DEBUG: No books found in search results');
        setIsSearching(false);
        return [];
      }
      
      console.log(`SEARCH DEBUG: Found ${searchData.docs.length} books in search results`);
      // Log first result as example
      if (searchData.docs.length > 0) {
        console.log('SEARCH DEBUG: First search result:', JSON.stringify(searchData.docs[0], null, 2));
      }
      
      // Step 2: Process each book in parallel
      const bookPromises = searchData.docs.slice(0, 5).map(async (book) => {
        try {
          // Get the best available identifier for detailed info
          let detailedBookData = null;
          
          // Prepare author information - OpenLibrary can have multiple authors
          let authorNames = book.author_name || ['Unknown Author'];
          let authorName = authorNames[0]; // Default to first author
          let allAuthors = authorNames.join(', '); // All authors joined for display if needed
          
          // Cover image URL if available
          let coverImageUrl = null;
          if (book.cover_i) {
            coverImageUrl = `https://covers.openlibrary.org/b/id/${book.cover_i}-M.jpg`;
          }
          
          // Get book details using the cover_edition_key or first edition_key
          let editionKey = null;
          
          // Try to get the best key for detailed information
          if (book.cover_edition_key) {
            editionKey = book.cover_edition_key;
            console.log(`BOOK DEBUG: Using cover_edition_key: ${editionKey}`);
          } else if (book.edition_key && Array.isArray(book.edition_key) && book.edition_key.length > 0) {
            editionKey = book.edition_key[0];
            console.log(`BOOK DEBUG: Using first edition_key: ${editionKey}`);
          } else {
            console.log('BOOK DEBUG: No edition key available for detailed data');
          }
          
          if (editionKey) {
            try {
              const detailsUrl = `https://openlibrary.org/books/${editionKey}.json`;
              console.log(`BOOK DEBUG: Fetching book details from: ${detailsUrl}`);
              
              const bookDetailsResponse = await fetch(detailsUrl);
              console.log(`BOOK DEBUG: Details fetch response status: ${bookDetailsResponse.status}`);
              
              if (bookDetailsResponse.ok) {
                const detailsText = await bookDetailsResponse.text();
                console.log(`BOOK DEBUG: Raw details response: ${detailsText.substring(0, 200)}...`);
                
                try {
                  detailedBookData = JSON.parse(detailsText);
                  console.log('BOOK DEBUG: Parsed book details data:', detailedBookData);
                  console.log('BOOK DEBUG: Fields available:', Object.keys(detailedBookData).join(', '));
                  
                  // Check specific fields we're interested in
                  if (detailedBookData.isbn_13) console.log('BOOK DEBUG: isbn_13 present:', detailedBookData.isbn_13);
                  if (detailedBookData.isbn_10) console.log('BOOK DEBUG: isbn_10 present:', detailedBookData.isbn_10);
                  if (detailedBookData.number_of_pages) console.log('BOOK DEBUG: number_of_pages present:', detailedBookData.number_of_pages);
                  if (detailedBookData.publishers) console.log('BOOK DEBUG: publishers present:', detailedBookData.publishers);
                } catch (parseError) {
                  console.error('BOOK DEBUG: Error parsing book details JSON:', parseError);
                }
                
                // Fetch author details for each author in the detailed data
                if (detailedBookData && detailedBookData.authors && Array.isArray(detailedBookData.authors) && detailedBookData.authors.length > 0) {
                  try {
                    // Get the first author key from the detailed data
                    const authorKeyFull = detailedBookData.authors[0].key;
                    console.log('AUTHOR DEBUG: Author key from details:', authorKeyFull);
                    
                    if (authorKeyFull) {
                      // Extract just the ID portion from the key (e.g., "OL242220A" from "/authors/OL242220A")
                      const authorKey = authorKeyFull.split('/').pop();
                      console.log('AUTHOR DEBUG: Extracted author ID:', authorKey);
                      
                      const authorUrl = `https://openlibrary.org/authors/${authorKey}.json`;
                      console.log('AUTHOR DEBUG: Fetching author from:', authorUrl);
                      
                      const authorResponse = await fetch(authorUrl);
                      console.log('AUTHOR DEBUG: Author response status:', authorResponse.status);
                      
                      if (authorResponse.ok) {
                        const authorData = await authorResponse.json();
                        console.log('AUTHOR DEBUG: Author data received:', authorData);
                        if (authorData.name) {
                          authorName = authorData.name; // Update with full author name
                          console.log('AUTHOR DEBUG: Updated author name to:', authorName);
                        }
                      }
                    }
                  } catch (error) {
                    console.error('AUTHOR DEBUG: Error fetching author details:', error);
                    // Continue with the basic author name we already have
                  }
                } else if (book.author_key && book.author_key.length > 0) {
                  // Fallback to search result author key if detailed data doesn't have authors
                  try {
                    const authorKey = book.author_key[0];
                    const authorResponse = await fetch(`https://openlibrary.org/authors/${authorKey}.json`);
                    
                    if (authorResponse.ok) {
                      const authorData = await authorResponse.json();
                      if (authorData.name) {
                        authorName = authorData.name;
                      }
                    }
                  } catch (error) {
                    console.error('Error fetching author details from search result:', error);
                  }
                }
              }
            } catch (error) {
              console.error('Error fetching detailed book data:', error);
            }
          }
          
          // Get ISBN information from detailed book data
          let isbn13 = '';
          let isbn10 = '';
          let barcode = '';
          
          console.log('ISBN DEBUG: Beginning ISBN extraction');
          console.log('ISBN DEBUG: detailedBookData available:', !!detailedBookData);
          
          // Extract ISBN-13 (preferred) - Example: "isbn_13": ["9781612680194"]
          if (detailedBookData) {
            console.log('ISBN DEBUG: detailedBookData type:', typeof detailedBookData);
            
            // Check if isbn_13 exists
            if ('isbn_13' in detailedBookData) {
              console.log('ISBN DEBUG: isbn_13 field exists:', detailedBookData.isbn_13);
              console.log('ISBN DEBUG: isbn_13 type:', typeof detailedBookData.isbn_13);
              
              if (Array.isArray(detailedBookData.isbn_13)) {
                console.log('ISBN DEBUG: isbn_13 is array with length:', detailedBookData.isbn_13.length);
                if (detailedBookData.isbn_13.length > 0) {
                  isbn13 = detailedBookData.isbn_13[0];
                  console.log('ISBN DEBUG: Extracted ISBN-13:', isbn13);
                }
              } else if (typeof detailedBookData.isbn_13 === 'string') {
                isbn13 = detailedBookData.isbn_13;
                console.log('ISBN DEBUG: Extracted ISBN-13 from string:', isbn13);
              }
            } else {
              console.log('ISBN DEBUG: No isbn_13 field found in detailedBookData');
            }
            
            // Check if isbn_10 exists
            if ('isbn_10' in detailedBookData) {
              console.log('ISBN DEBUG: isbn_10 field exists:', detailedBookData.isbn_10);
              console.log('ISBN DEBUG: isbn_10 type:', typeof detailedBookData.isbn_10);
              
              if (Array.isArray(detailedBookData.isbn_10)) {
                console.log('ISBN DEBUG: isbn_10 is array with length:', detailedBookData.isbn_10.length);
                if (detailedBookData.isbn_10.length > 0) {
                  isbn10 = detailedBookData.isbn_10[0];
                  console.log('ISBN DEBUG: Extracted ISBN-10:', isbn10);
                }
              } else if (typeof detailedBookData.isbn_10 === 'string') {
                isbn10 = detailedBookData.isbn_10;
                console.log('ISBN DEBUG: Extracted ISBN-10 from string:', isbn10);
              }
            } else {
              console.log('ISBN DEBUG: No isbn_10 field found in detailedBookData');
            }
            
            // Try with identifiers as last resort
            if (!isbn13 && !isbn10 && detailedBookData.identifiers) {
              console.log('ISBN DEBUG: Checking identifiers object:', detailedBookData.identifiers);
              if (detailedBookData.identifiers.isbn_13) {
                isbn13 = Array.isArray(detailedBookData.identifiers.isbn_13) ? 
                  detailedBookData.identifiers.isbn_13[0] : detailedBookData.identifiers.isbn_13;
                console.log('ISBN DEBUG: Found ISBN-13 in identifiers:', isbn13);
              }
              if (detailedBookData.identifiers.isbn_10) {
                isbn10 = Array.isArray(detailedBookData.identifiers.isbn_10) ? 
                  detailedBookData.identifiers.isbn_10[0] : detailedBookData.identifiers.isbn_10;
                console.log('ISBN DEBUG: Found ISBN-10 in identifiers:', isbn10);
              }
            }
          }
          
          // Fallback to top-level ISBN if needed
          let topLevelIsbn = '';
          if (book.isbn && Array.isArray(book.isbn) && book.isbn.length > 0) {
            topLevelIsbn = book.isbn[0];
          } else if (book.isbn) {
            topLevelIsbn = book.isbn;
          }
          
          // Set barcode priority: ISBN-13 > ISBN-10 > search result ISBN
          barcode = isbn13 || isbn10 || topLevelIsbn;
          
          // Get keywords from subjects or subject_facet
          let keywords = [];
          
          // Collect keywords from different possible sources
          if (book.subject && Array.isArray(book.subject)) {
            keywords = [...keywords, ...book.subject.slice(0, 5)];
          } 
          
          if (book.subject_facet && Array.isArray(book.subject_facet)) {
            keywords = [...keywords, ...book.subject_facet.slice(0, 5)];
          }
          
          // Get a unique list of keywords, limited to 5
          const uniqueKeywords = [...new Set(keywords)].slice(0, 5).join(', ');
          
          // Determine category and subject based on subjects
          let category = 'Unknown';
          let subject = 'General';
          let subjects = [];
          
          // Collect all possible subject sources
          if (book.subject) subjects = subjects.concat(book.subject || []);
          if (book.subject_facet) subjects = subjects.concat(book.subject_facet || []);
          
          // Determine fiction vs non-fiction and specific subject
          if (subjects.length > 0) {
            if (subjects.some(s => s && typeof s === 'string' && s.includes('Fiction'))) {
              category = 'Fiction';
              // Find more specific subject
              if (subjects.some(s => s && typeof s === 'string' && s.includes('Fantasy'))) subject = 'Fantasy';
              else if (subjects.some(s => s && typeof s === 'string' && s.includes('Science Fiction'))) subject = 'Science Fiction';
              else if (subjects.some(s => s && typeof s === 'string' && s.includes('Mystery'))) subject = 'Mystery';
              else if (subjects.some(s => s && typeof s === 'string' && s.includes('Romance'))) subject = 'Romance';
              else if (subjects.some(s => s && typeof s === 'string' && s.includes('Historical'))) subject = 'Historical Fiction';
              else subject = 'Literary Fiction';
            } else {
              category = 'Non-Fiction';
              // Find more specific subject
              if (subjects.some(s => s && typeof s === 'string' && s.includes('History'))) subject = 'History';
              else if (subjects.some(s => s && typeof s === 'string' && s.includes('Business'))) subject = 'Business';
              else if (subjects.some(s => s && typeof s === 'string' && s.includes('Finance'))) subject = 'Finance';
              else if (subjects.some(s => s && typeof s === 'string' && s.includes('Science'))) subject = 'Science';
              else if (subjects.some(s => s && typeof s === 'string' && s.includes('Philosophy'))) subject = 'Philosophy';
              else if (subjects.some(s => s && typeof s === 'string' && s.includes('Technology'))) subject = 'Technology';
              else if (subjects.some(s => s && typeof s === 'string' && s.includes('Computer'))) subject = 'Computer Science';
              else if (subjects.some(s => s && typeof s === 'string' && s.includes('Self-help'))) subject = 'Self-Help';
              else subject = 'General Knowledge';
            }
          }
          
          // Get description - could be in different formats in the API
          let description = '';
          if (detailedBookData) {
            if (detailedBookData.description) {
              if (typeof detailedBookData.description === 'string') {
                description = detailedBookData.description;
              } else if (typeof detailedBookData.description === 'object') {
                description = detailedBookData.description.value || '';
              }
            }
            // If no description found, try to construct one from available data
            if (!description) {
              description = `${book.title} by ${authorName}`;
              if (detailedBookData.publishers && detailedBookData.publishers.length > 0) {
                description += `, published by ${detailedBookData.publishers[0]}`;
              }
              if (detailedBookData.publish_date) {
                description += ` (${detailedBookData.publish_date})`;
              }
            }
          } else if (!description) {
            description = `${book.title} by ${authorName}`;
          }
          
          // Get publisher information - Example: "publishers": ["Plata Publishing"]
          let publisher = '';
          if (detailedBookData && detailedBookData.publishers) {
            if (Array.isArray(detailedBookData.publishers) && detailedBookData.publishers.length > 0) {
              publisher = detailedBookData.publishers[0];
              console.log('Found publisher:', publisher);
            } else if (typeof detailedBookData.publishers === 'string') {
              publisher = detailedBookData.publishers;
            }
          } else if (book.publisher) {
            publisher = Array.isArray(book.publisher) ? book.publisher[0] : book.publisher;
          }
          
          // Get edition information
          let edition = '';
          // Use cover_edition_key as the primary edition value
          if (book.cover_edition_key) {
            edition = book.cover_edition_key;
            console.log('EDITION DEBUG: Using cover_edition_key for edition:', edition);
          } else if (detailedBookData && detailedBookData.edition_name) {
            edition = detailedBookData.edition_name;
            console.log('EDITION DEBUG: Using detailed book edition_name for edition:', edition);
          } else if (book.edition_name) {
            edition = book.edition_name;
            console.log('EDITION DEBUG: Using book edition_name for edition:', edition);
          }
          
          // Use the previously defined editionKey for metadata
          
          // Format publication date
          let publication_date = '';
          if (detailedBookData && detailedBookData.publish_date) {
            publication_date = detailedBookData.publish_date;
          } else if (book.publish_date && book.publish_date.length > 0) {
            publication_date = book.publish_date[0];
          }
          
          // Try to convert string date to ISO format if possible
          try {
            const date = new Date(publication_date);
            if (!isNaN(date.getTime())) {
              publication_date = date.toISOString().split('T')[0]; // YYYY-MM-DD format
            }
          } catch (e) {
            // Keep the original string format if parsing fails
          }
          
          // Return formatted book data
          console.log('FIELDS DEBUG: Preparing to extract fields from book data');
          console.log('FIELDS DEBUG: detailedBookData available:', !!detailedBookData);
          if (detailedBookData) {
            console.log('FIELDS DEBUG: Keys in detailedBookData:', Object.keys(detailedBookData).join(', '));
          }
          
          // Extract number of pages - Example: "number_of_pages": 336
          let pages = '';
          if (detailedBookData && detailedBookData.number_of_pages !== undefined) {
            console.log('FIELDS DEBUG: Found number_of_pages:', detailedBookData.number_of_pages);
            // Convert to string to ensure compatibility with form fields
            pages = detailedBookData.number_of_pages.toString();
            console.log('FIELDS DEBUG: Set pages to:', pages);
          } else {
            console.log('FIELDS DEBUG: number_of_pages field not found in detailedBookData');
            if (detailedBookData) {
              console.log('FIELDS DEBUG: detailedBookData content:', JSON.stringify(detailedBookData).substring(0, 200));
            }
          }
          
          // Return formatted book data with isbn and pages
          console.log('FINAL DEBUG: Creating final book data');
          console.log('FINAL DEBUG: Edition:', edition);
          console.log('FINAL DEBUG: ISBN-13:', isbn13);
          console.log('FINAL DEBUG: ISBN-10:', isbn10);
          console.log('FINAL DEBUG: Pages:', pages);
          
          return {
            title: book.title,
            author: authorName,
            description: description || `Book by ${authorName}`,
            barcode: barcode,
            isbn: isbn13 || isbn10 || barcode, // Use ISBN-13 if available, then ISBN-10, then barcode
            edition: edition || '',
            series: '',  // OpenLibrary doesn't directly provide series info
            category: category,
            subject: subject,
            keywords: uniqueKeywords,
            publisher: publisher,
            publication_date: publication_date,
            pages: pages, // Number of pages
            // Additional metadata that might be useful for debugging or enhanced features
            _meta: {
              cover_image_url: coverImageUrl,
              all_authors: allAuthors,
              isbn_13: isbn13,
              isbn_10: isbn10,
              number_of_pages: detailedBookData?.number_of_pages,
              first_publish_year: book.first_publish_year,
              cover_edition_key: book.cover_edition_key,
              edition_key: book.edition_key && book.edition_key.length > 0 ? book.edition_key[0] : '',
              language: book.language && Array.isArray(book.language) ? book.language[0] : null
            }
          };
        } catch (error) {
          console.error('Error processing book details:', error);
          // Return basic info if detailed fetch fails
          let fallbackAuthor = book.author_name ? book.author_name[0] : 'Unknown Author';
          let fallbackPublisher = book.publisher ? (Array.isArray(book.publisher) ? book.publisher[0] : book.publisher) : '';
          
          // Extract any available ISBN information for fallback case
          let isbn13 = '';
          let isbn10 = '';
          let fallbackISBN = '';
          
          // If we have an ISBN array, check if any are ISBN-13 (13 digits) or ISBN-10 (10 digits)
          if (book.isbn && Array.isArray(book.isbn) && book.isbn.length > 0) {
            for (const isbn of book.isbn) {
              const cleanIsbn = isbn.replace(/[^0-9X]/g, '');
              if (cleanIsbn.length === 13) {
                isbn13 = isbn;
                break; // Prefer ISBN-13
              } else if (cleanIsbn.length === 10) {
                isbn10 = isbn;
              }
            }
            // If we didn't find an ISBN-13, use the ISBN-10 if available
            fallbackISBN = isbn13 || isbn10 || book.isbn[0];
          } else if (book.isbn) {
            fallbackISBN = book.isbn;
          }
          
          console.log('FALLBACK DEBUG: Creating fallback data with ISBN:', fallbackISBN);
          
          return {
            title: book.title,
            author: fallbackAuthor,
            description: `${book.title} by ${fallbackAuthor}`,
            barcode: fallbackISBN,
            isbn: isbn13 || isbn10 || fallbackISBN, // Use ISBN-13 if available, then ISBN-10
            edition: '',
            series: '',
            category: '',
            subject: '',
            keywords: '',
            publisher: fallbackPublisher,
            publication_date: book.publish_date ? book.publish_date[0] : '',
            pages: '', // Empty pages field
            // Include minimal metadata even in fallback
            _meta: {
              cover_image_url: book.cover_i ? `https://covers.openlibrary.org/b/id/${book.cover_i}-M.jpg` : null,
              all_authors: book.author_name ? book.author_name.join(', ') : 'Unknown Author',
              isbn_13: isbn13,
              isbn_10: isbn10,
              first_publish_year: book.first_publish_year,
              language: book.language && Array.isArray(book.language) ? book.language[0] : null
            }
          };
        }
      });
      
      const results = await Promise.all(bookPromises);
      setIsSearching(false);
      return results;
      
    } catch (error) {
      console.error('Error searching books:', error);
      setIsSearching(false);
      return [];
    }
  };
  
  // Note: In a production environment, this API call should be moved to your Search Service
  // at port 5004 to avoid exposing API calls directly from the frontend and to maintain
  // your microservices architecture.

  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // Handle title search for autocomplete
    if (name === 'title') {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
      
      if (value.length >= 2) {
        // Search for book suggestions
        searchBooks(value).then(results => {
          setBookSuggestions(results);
          setShowSuggestions(results.length > 0);
        });
      } else {
        setBookSuggestions([]);
        setShowSuggestions(false);
      }
      return;
    }
    
    // For numerical inputs, ensure valid numbers
    if (['pages', 'total_copies', 'available_copies', 'duration'].includes(name)) {
      // Allow empty or valid numbers
      if (value === '' || !isNaN(value)) {
        setFormData(prev => ({
          ...prev,
          [name]: value
        }));
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  // Handle selecting a book from suggestions
  const handleSelectBook = (book) => {
    console.log('SELECT DEBUG: Selected book data:', book);
    console.log('SELECT DEBUG: ISBN:', book.isbn);
    console.log('SELECT DEBUG: Pages:', book.pages);
    
    setFormData(prev => ({
      ...prev,  // Preserve all existing values including document_type
      title: book.title,
      description: book.description,
      barcode: book.barcode,
      isbn: book.isbn || '', // Add ISBN field
      author: book.author,
      edition: book.edition,
      series: book.series,
      category: book.category,
      subject: book.subject,
      keywords: book.keywords,
      publisher: book.publisher,
      publication_date: book.publication_date,
      pages: book.pages || '', // Add pages field
      // Default document_type to 'book' if not already set
      document_type: prev.document_type || 'book'
    }));
    
    console.log('SELECT DEBUG: Document type preserved/set to book as default if not already set');
    
    setShowSuggestions(false);
    setBookSuggestions([]);
  };

  const handleGenerateBarcode = async () => {
    try {
      setGeneratingBarcode(true);
      const response = await generateBarcode();
      setFormData(prev => ({
        ...prev,
        barcode: response.barcode
      }));
    } catch (err) {
      setError('Failed to generate barcode');
    } finally {
      setGeneratingBarcode(false);
    }
  };

  // Function to classify document using GROQ AI
  const handleClassifyDocument = async () => {
    try {
      const GROQ_API_KEY = '********************************************************';
      const promptTemplate = `Voici un document avec les informations suivantes :

Titre : ${formData.title}  
Description : ${formData.description}

Peux-tu suggérer :
- Une catégorie (ex : Science, Non-Fiction, etc.)
- Un sujet (ex : Informatique, Programmation, etc.)
- 3 à 5 mots-clés pertinents

Réponds dans un format JSON.
{
  "category": "Science",
  "subject": "Computer Science",
  "keywords": ["Python", "programmation", "débutants", "guide", "coding"]
}`;

      // Mock API call for development (in production, use actual fetch)
      // Normally this would be a server-side call to protect the API key
      console.log('Classifying document with GROQ AI...');
      console.log('API call would be:');
      console.log(`curl https://api.groq.com/openai/v1/chat/completions \\`);
      console.log(`-H "Content-Type: application/json" \\`);
      console.log(`-H "Authorization: Bearer ${GROQ_API_KEY}" \\`);
      console.log(`-d '{`);
      console.log(`"model": "llama3-70b-8192",`);
      console.log(`"messages": [{`);
      console.log(`    "role": "user",`);
      console.log(`    "content": "${promptTemplate.replace(/\n/g, '\\n')}"`);
      console.log(`}]`);
      console.log(`}'`);
      
      // For demonstration, we'll simulate a response
      // In production, uncomment the fetch code below and call the classification service
      
      /*
      // Option 1: Direct GROQ API call (not recommended for production)
      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${GROQ_API_KEY}`
        },
        body: JSON.stringify({
          model: 'llama3-70b-8192',
          messages: [{
            role: 'user',
            content: promptTemplate
          }]
        })
      });
      
      // Option 2: Use classification-service (recommended)
      const response = await fetch('http://localhost:5003/classify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description
        })
      });
      
      const data = await response.json();
      const resultJson = data.classification || JSON.parse(data.choices[0].message.content);
      setClassificationResult(resultJson);
      */
      
      // Simulate classification result for demonstration
      setTimeout(() => {
        // Example result that would come from the API
        let resultExample;
        
        // Customize based on document
        if (formData.title.toLowerCase().includes('python')) {
          resultExample = {
            category: 'Science',
            subject: 'Computer Science',
            keywords: ['Python', 'programmation', 'débutants', 'guide', 'coding']
          };
        } else if (formData.title.toLowerCase().includes('histoire')) {
          resultExample = {
            category: 'Non-Fiction',
            subject: 'Histoire',
            keywords: ['histoire', 'civilisation', 'documentaire', 'recherche']
          };
        } else {
          // Default fallback
          resultExample = {
            category: 'Literature',
            subject: formData.title.includes('novel') ? 'Fiction' : 'General Knowledge',
            keywords: ['livre', 'information', 'connaissance', 'éducation']
          };
        }
        
        setClassificationResult(resultExample);
        setIsClassifying(false);
      }, 1500);
      
    } catch (error) {
      console.error('Error classifying document:', error);
      alert('Error classifying document. Please try again.');
      setIsClassifying(false);
    }
  };
  
  // Auto-trigger classification when isClassifying becomes true
  useEffect(() => {
    if (isClassifying && formData.title && formData.description) {
      handleClassifyDocument();
    }
  }, [isClassifying]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      setError('');
      
      let result;
      let submitData = { ...formData };
      
      // Remove empty strings for certain fields
      Object.keys(submitData).forEach(key => {
        if (submitData[key] === '') {
          submitData[key] = null;
        }
      });
      
      if (initialDocument && initialDocument.id) {
        console.log('FORM DEBUG: Updating document with ID:', initialDocument.id);
        result = await updateDocument(initialDocument.id, submitData);
        console.log('FORM DEBUG: Update result:', result);

        // Show success message
        alert(`Document updated successfully! ${result.changes ? Object.keys(result.changes).length : 0} fields were changed.`);
      } else {
        result = await createDocument(submitData);
        console.log('FORM DEBUG: Create result:', result);

        // Show success message
        alert('Document created successfully!');
      }

      // Notify parent component about the save (this will trigger refresh and close form)
      if (onSave) {
        console.log('FORM DEBUG: Calling onSave with document:', result.document);
        onSave(result.document);
      }
    } catch (err) {
      console.error('Form submission error:', err);
      setError(err.message || 'An error occurred while saving the document');
    } finally {
      setLoading(false);
    }
  };

  const renderTypeSpecificFields = () => {
    switch (formData.document_type) {
      case 'book':
        return (
          <div
            className="form-section transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <h3 style={{ color: colors.text }}>
              {t('documents.bookInformation') || 'Book Information'}
            </h3>
            <div className="form-group">
              <label
                htmlFor="author"
                style={{ color: colors.text }}
              >
                {t('documents.author') || 'Author'} *
              </label>
              <input
                type="text"
                id="author"
                name="author"
                value={formData.author}
                onChange={handleChange}
                required
                className="transition-colors duration-200"
                style={{
                  backgroundColor: colors.background,
                  color: colors.text,
                  borderColor: colors.border
                }}
              />
            </div>
            <div className="form-row">
              <div className="form-group">
                <label
                  htmlFor="edition"
                  style={{ color: colors.text }}
                >
                  {t('documents.edition') || 'Edition'}
                </label>
                <input
                  type="text"
                  id="edition"
                  name="edition"
                  value={formData.edition}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>
              <div className="form-group">
                <label
                  htmlFor="series"
                  style={{ color: colors.text }}
                >
                  {t('documents.series') || 'Series'}
                </label>
                <input
                  type="text"
                  id="series"
                  name="series"
                  value={formData.series}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>
            </div>
          </div>
        );

      case 'periodical':
        return (
          <div
            className="form-section transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <h3 style={{ color: colors.text }}>
              {t('documents.periodicalInformation') || 'Periodical Information'}
            </h3>
            <div className="form-row">
              <div className="form-group">
                <label
                  htmlFor="issn"
                  style={{ color: colors.text }}
                >
                  ISSN
                </label>
                <input
                  type="text"
                  id="issn"
                  name="issn"
                  value={formData.issn}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>
              <div className="form-group">
                <label
                  htmlFor="frequency"
                  style={{ color: colors.text }}
                >
                  {t('documents.frequency') || 'Frequency'}
                </label>
                <select
                  id="frequency"
                  name="frequency"
                  value={formData.frequency}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                >
                  <option value="">Select frequency</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                  <option value="quarterly">Quarterly</option>
                  <option value="annually">Annually</option>
                </select>
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label
                  htmlFor="volume"
                  style={{ color: colors.text }}
                >
                  {t('documents.volume') || 'Volume'}
                </label>
                <input
                  type="text"
                  id="volume"
                  name="volume"
                  value={formData.volume}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>
              <div className="form-group">
                <label
                  htmlFor="issue"
                  style={{ color: colors.text }}
                >
                  {t('documents.issue') || 'Issue'}
                </label>
                <input
                  type="text"
                  id="issue"
                  name="issue"
                  value={formData.issue}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>
            </div>
          </div>
        );

      case 'article':
        return (
          <div
            className="form-section transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <h3 style={{ color: colors.text }}>
              {t('documents.articleInformation') || 'Article Information'}
            </h3>
            <div className="form-group">
              <label
                htmlFor="author"
                style={{ color: colors.text }}
              >
                {t('documents.author') || 'Author'} *
              </label>
              <input
                type="text"
                id="author"
                name="author"
                value={formData.author}
                onChange={handleChange}
                required
                className="transition-colors duration-200"
                style={{
                  backgroundColor: colors.background,
                  color: colors.text,
                  borderColor: colors.border
                }}
              />
            </div>
            <div className="form-row">
              <div className="form-group">
                <label
                  htmlFor="journal"
                  style={{ color: colors.text }}
                >
                  {t('documents.journal') || 'Journal'}
                </label>
                <input
                  type="text"
                  id="journal"
                  name="journal"
                  value={formData.journal}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>
              <div className="form-group">
                <label
                  htmlFor="doi"
                  style={{ color: colors.text }}
                >
                  DOI
                </label>
                <input
                  type="text"
                  id="doi"
                  name="doi"
                  value={formData.doi}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>
            </div>
          </div>
        );

      case 'video':
        return (
          <div
            className="form-section transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <h3 style={{ color: colors.text }}>
              {t('documents.videoInformation') || 'Video Information'}
            </h3>
            <div className="form-row">
              <div className="form-group">
                <label
                  htmlFor="director"
                  style={{ color: colors.text }}
                >
                  {t('documents.director') || 'Director'}
                </label>
                <input
                  type="text"
                  id="director"
                  name="director"
                  value={formData.director}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>
              <div className="form-group">
                <label
                  htmlFor="duration"
                  style={{ color: colors.text }}
                >
                  {t('documents.duration') || 'Duration'} (minutes)
                </label>
                <input
                  type="number"
                  id="duration"
                  name="duration"
                  value={formData.duration}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>
            </div>
            <div className="form-group">
              <label
                htmlFor="resolution"
                style={{ color: colors.text }}
              >
                {t('documents.resolution') || 'Resolution'}
              </label>
              <select
                id="resolution"
                name="resolution"
                value={formData.resolution}
                onChange={handleChange}
                className="transition-colors duration-200"
                style={{
                  backgroundColor: colors.background,
                  color: colors.text,
                  borderColor: colors.border
                }}
              >
                <option value="">Select resolution</option>
                <option value="720p">720p</option>
                <option value="1080p">1080p</option>
                <option value="4K">4K</option>
                <option value="8K">8K</option>
              </select>
            </div>
          </div>
        );

      default:
        return null;
    }
  };



  return (
    <div
      className="document-form-modal-overlay"
      style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
    >
      <div
        className="document-form-container transition-colors duration-200"
        style={{
          backgroundColor: colors.background,
          color: colors.text,
          border: `1px solid ${colors.border}`
        }}
      >
        <div
          className="document-form-header transition-colors duration-200"
          style={{
            backgroundColor: colors.surface,
            borderBottomColor: colors.border
          }}
        >
          <h2 style={{ color: colors.text }}>
            {isEditing ? (t('documents.editDocument') || 'Edit Document') : (t('documents.addDocument') || 'Add New Document')}
          </h2>
          <button
            type="button"
            className="close-btn transition-colors duration-200"
            onClick={onCancel}
            style={{
              color: colors.textSecondary,
              backgroundColor: 'transparent'
            }}
          >
            ×
          </button>
        </div>

        <div
          className="document-form-content transition-colors duration-200"
          style={{ backgroundColor: colors.background }}
        >
          {error && (
            <div
              className="error-message transition-colors duration-200"
              style={{
                backgroundColor: colors.error + '20',
                color: colors.error,
                borderColor: colors.error
              }}
            >
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
          {/* Basic Information */}
          <div
            className="form-section transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <h3 style={{ color: colors.text }}>
              {t('documents.basicInformation') || 'Basic Information'}
            </h3>

            <div className="form-group">
              <label
                htmlFor="title"
                style={{ color: colors.text }}
              >
                {t('documents.title') || 'Title'} *
              </label>
              <div className="title-input-container" ref={suggestionRef}>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  autoComplete="off"
                  required
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
                {isSearching && (
                  <div className="mini-loader"></div>
                )}
                {showSuggestions && bookSuggestions.length > 0 && (
                  <ul
                    className="book-suggestions transition-colors duration-200"
                    style={{
                      backgroundColor: colors.surface,
                      borderColor: colors.border,
                      boxShadow: `0 4px 12px ${colors.shadow}`
                    }}
                  >
                    {bookSuggestions.map((book, index) => (
                      <li
                        key={index}
                        onClick={() => handleSelectBook(book)}
                        className="suggestion-item transition-colors duration-200"
                        style={{
                          backgroundColor: colors.background,
                          borderBottomColor: colors.border
                        }}
                      >
                        <div
                          className="suggestion-title"
                          style={{ color: colors.text }}
                        >
                          {book.title}
                        </div>
                        <div
                          className="suggestion-author"
                          style={{ color: colors.textSecondary }}
                        >
                          by {book.author}
                        </div>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>

            <div className="form-group">
              <label
                htmlFor="description"
                style={{ color: colors.text }}
              >
                {t('documents.description') || 'Description'}
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows="3"
                className="transition-colors duration-200"
                style={{
                  backgroundColor: colors.background,
                  color: colors.text,
                  borderColor: colors.border
                }}
              />
            </div>

            <div className="form-row">
              <div className="form-group">
                <label
                  htmlFor="document_type"
                  style={{ color: colors.text }}
                >
                  {t('documents.documentType') || 'Document Type'} *
                </label>
                <select
                  id="document_type"
                  name="document_type"
                  value={formData.document_type}
                  onChange={handleChange}
                  required
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                >
                  {DOCUMENT_TYPES.filter(type => ['book', 'periodical', 'video'].includes(type.value)).map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label
                  htmlFor="barcode"
                  style={{ color: colors.text }}
                >
                  {t('documents.barcode') || 'Barcode'} *
                </label>
                <div className="barcode-input">
                  <input
                    type="text"
                    id="barcode"
                    name="barcode"
                    value={formData.barcode}
                    onChange={handleChange}
                    required
                    className="transition-colors duration-200"
                    style={{
                      backgroundColor: colors.background,
                      color: colors.text,
                      borderColor: colors.border
                    }}
                  />
                  {!isEditing && (
                    <button
                      type="button"
                      onClick={handleGenerateBarcode}
                      disabled={generatingBarcode}
                      className="generate-barcode-btn transition-colors duration-200"
                      style={{
                        backgroundColor: colors.primary,
                        color: 'white',
                        borderColor: colors.primary
                      }}
                    >
                      {generatingBarcode ? (t('common.generating') || 'Generating...') : (t('common.generate') || 'Generate')}
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Type-specific fields */}
          {renderTypeSpecificFields()}

          {/* Classification */}
          <div
            className="form-section transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <h3 style={{ color: colors.text }}>
              {t('documents.classification') || 'Classification'}
            </h3>

            {!isClassifying && !classificationResult ? (
              <>
                <div className="form-row">
                  <div className="form-group">
                    <label
                      htmlFor="category"
                      style={{ color: colors.text }}
                    >
                      {t('documents.category') || 'Category'}
                    </label>
                    <input
                      type="text"
                      id="category"
                      name="category"
                      value={formData.category}
                      onChange={handleChange}
                      list="categories"
                      className="transition-colors duration-200"
                      style={{
                        backgroundColor: colors.background,
                        color: colors.text,
                        borderColor: colors.border
                      }}
                    />
                    <datalist id="categories">
                      {COMMON_CATEGORIES.map(cat => (
                        <option key={cat} value={cat} />
                      ))}
                    </datalist>
                  </div>

                  <div className="form-group">
                    <label
                      htmlFor="subject"
                      style={{ color: colors.text }}
                    >
                      {t('documents.subject') || 'Subject'}
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      list="subjects"
                      className="transition-colors duration-200"
                      style={{
                        backgroundColor: colors.background,
                        color: colors.text,
                        borderColor: colors.border
                      }}
                    />
                    <datalist id="subjects">
                      {COMMON_SUBJECTS.map(subj => (
                        <option key={subj} value={subj} />
                      ))}
                    </datalist>
                  </div>
                </div>

                <div className="form-group">
                  <label
                    htmlFor="keywords"
                    style={{ color: colors.text }}
                  >
                    {t('documents.keywords') || 'Keywords'} (comma-separated)
                  </label>
                  <input
                    type="text"
                    id="keywords"
                    name="keywords"
                    value={formData.keywords}
                    onChange={handleChange}
                    placeholder="keyword1, keyword2, keyword3"
                    className="transition-colors duration-200"
                    style={{
                      backgroundColor: colors.background,
                      color: colors.text,
                      borderColor: colors.border
                    }}
                  />
                </div>
                
                <div className="auto-classify-section">
                  <button
                    type="button"
                    className="btn-primary classify-btn"
                    onClick={() => {
                      if (formData.title && formData.description) {
                        setIsClassifying(true);
                      } else {
                        alert('Please enter a title and description before classifying.');
                      }
                    }}
                  >
                    🤖 Classify with GROQ AI
                  </button>
                  <p className="classify-note">AI-powered classification using GROQ API. Requires title and description.</p>
                </div>
              </>
            ) : isClassifying ? (
              <div className="classification-form">
                <h4>Classifying document with GROQ AI...</h4>
                <p>Using title and description to generate classification...</p>
                <div className="loader"></div>
                <button 
                  type="button"
                  className="btn-secondary"
                  onClick={() => setIsClassifying(false)}
                >
                  Cancel
                </button>
                <button 
                  type="button"
                  className="btn-primary"
                  onClick={() => handleClassifyDocument()}
                >
                  Force Classification
                </button>
              </div>
            ) : classificationResult && (
              <div className="classification-result">
                <h4>AI Classification Result</h4>
                
                <div className="result-card">
                  <div className="result-item">
                    <span className="result-label">Category:</span>
                    <span className="result-value">{classificationResult.category}</span>
                  </div>
                  
                  <div className="result-item">
                    <span className="result-label">Subject:</span>
                    <span className="result-value">{classificationResult.subject}</span>
                  </div>
                  
                  <div className="result-item">
                    <span className="result-label">Keywords:</span>
                    <div className="keywords-list">
                      {classificationResult.keywords.map((keyword, index) => (
                        <span key={index} className="keyword-tag">{keyword}</span>
                      ))}
                    </div>
                  </div>
                </div>
                
                <div className="result-actions">
                  <button 
                    type="button"
                    className="btn-secondary"
                    onClick={() => {
                      setClassificationResult(null);
                      setIsClassifying(false);
                    }}
                  >
                    Discard
                  </button>
                  <button 
                    type="button"
                    className="btn-primary"
                    onClick={() => {
                      // Apply classification to the form
                      setFormData(prev => ({
                        ...prev,
                        category: classificationResult.category,
                        subject: classificationResult.subject,
                        keywords: classificationResult.keywords.join(', ')
                      }));
                      setClassificationResult(null);
                      setIsClassifying(false);
                    }}
                  >
                    Apply Classification
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Publication Details */}
          <div
            className="form-section transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <h3 style={{ color: colors.text }}>
              {t('documents.publicationDetails') || 'Publication Details'}
            </h3>

            <div className="form-row">
              <div className="form-group">
                <label
                  htmlFor="publisher"
                  style={{ color: colors.text }}
                >
                  {t('documents.publisher') || 'Publisher'}
                </label>
                <input
                  type="text"
                  id="publisher"
                  name="publisher"
                  value={formData.publisher}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>

              <div className="form-group">
                <label
                  htmlFor="publication_date"
                  style={{ color: colors.text }}
                >
                  {t('documents.publicationDate') || 'Publication Date'}
                </label>
                <input
                  type="date"
                  id="publication_date"
                  name="publication_date"
                  value={formData.publication_date}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label
                  htmlFor="language"
                  style={{ color: colors.text }}
                >
                  {t('documents.language') || 'Language'}
                </label>
                <select
                  id="language"
                  name="language"
                  value={formData.language}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                >
                  {LANGUAGE_OPTIONS.map(lang => (
                    <option key={lang.value} value={lang.value}>
                      {lang.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label
                  htmlFor="isbn"
                  style={{ color: colors.text }}
                >
                  ISBN
                </label>
                <input
                  type="text"
                  id="isbn"
                  name="isbn"
                  value={formData.isbn}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>
            </div>
          </div>

          {/* Physical Details */}
          <div
            className="form-section transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <h3 style={{ color: colors.text }}>
              {t('documents.physicalDetails') || 'Physical Details'}
            </h3>

            <div className="form-row">
              <div className="form-group">
                <label
                  htmlFor="pages"
                  style={{ color: colors.text }}
                >
                  {t('documents.pages') || 'Pages'}
                </label>
                <input
                  type="number"
                  id="pages"
                  name="pages"
                  value={formData.pages}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>

              <div className="form-group">
                <label
                  htmlFor="format"
                  style={{ color: colors.text }}
                >
                  {t('documents.format') || 'Format'}
                </label>
                <select
                  id="format"
                  name="format"
                  value={formData.format}
                  onChange={handleChange}
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                >
                  <option value="">Select format</option>
                  {FORMAT_OPTIONS.map(format => (
                    <option key={format.value} value={format.value}>
                      {format.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="form-group">
              <label
                htmlFor="location"
                style={{ color: colors.text }}
              >
                {t('documents.shelfLocation') || 'Shelf Location'}
              </label>
              <input
                type="text"
                id="location"
                name="location"
                value={formData.location}
                onChange={handleChange}
                placeholder="e.g., A1-B2-C3"
                className="transition-colors duration-200"
                style={{
                  backgroundColor: colors.background,
                  color: colors.text,
                  borderColor: colors.border
                }}
              />
            </div>
          </div>

          {/* Availability */}
          <div
            className="form-section transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <h3 style={{ color: colors.text }}>
              {t('documents.availability') || 'Availability'}
            </h3>

            <div className="form-row">
              <div className="form-group">
                <label
                  htmlFor="total_copies"
                  style={{ color: colors.text }}
                >
                  {t('documents.totalCopies') || 'Total Copies'}
                </label>
                <input
                  type="number"
                  id="total_copies"
                  name="total_copies"
                  value={formData.total_copies}
                  onChange={handleChange}
                  min="1"
                  required
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>

              <div className="form-group">
                <label
                  htmlFor="available_copies"
                  style={{ color: colors.text }}
                >
                  {t('documents.availableCopies') || 'Available Copies'}
                </label>
                <input
                  type="number"
                  id="available_copies"
                  name="available_copies"
                  value={formData.available_copies}
                  onChange={handleChange}
                  min="0"
                  max={formData.total_copies}
                  required
                  className="transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
              </div>
            </div>
          </div>

          {/* Image */}
          <div
            className="form-section transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <h3 style={{ color: colors.text }}>
              {t('documents.image') || 'Image'}
            </h3>

            <div className="form-group">
              <label style={{ color: colors.text }}>
                {t('documents.documentImage') || 'Document Image'}
              </label>
              <input
                type="file"
                accept="image/*"
                className="transition-colors duration-200"
                style={{
                  backgroundColor: colors.background,
                  color: colors.text,
                  borderColor: colors.border
                }}
                onChange={(e) => {
                  const file = e.target.files[0];
                  if (file) {
                    // Validate file size (max 16MB to match backend)
                    if (file.size > 16 * 1024 * 1024) {
                      setError('Image file size must be less than 16MB');
                      return;
                    }

                    // Validate file type
                    const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/gif'];
                    if (!allowedTypes.includes(file.type)) {
                      setError('Only PNG, JPG, JPEG, and GIF images are allowed');
                      return;
                    }

                    console.log('IMAGE DEBUG: Selected new image file:', file.name, 'Size:', file.size, 'Type:', file.type);
                    setError(''); // Clear any previous errors
                    setFormData(prev => ({
                      ...prev,
                      image: file,
                      image_path: file.name // This will be updated by the server
                    }));
                  }
                }}
              />
              {formData.image_path && (
                <div className="image-preview">
                  <div className="image-info">
                    <button
                      type="button"
                      className="btn-secondary remove-image-btn"
                      onClick={() => {
                        console.log('IMAGE DEBUG: Removing current image');
                        setFormData(prev => ({
                          ...prev,
                          image: null,
                          image_path: ''
                        }));
                        // Clear the file input
                        const fileInput = document.querySelector('input[type="file"]');
                        if (fileInput) fileInput.value = '';
                      }}
                      style={{ marginLeft: '10px', padding: '5px 10px', fontSize: '12px' }}
                    >
                      Remove Image
                    </button>
                  </div>
                  {formData.image ? (
                    <div className="new-image-preview">
                      <p style={{ color: 'green', fontWeight: 'bold' }}>New image selected:</p>
                      <img
                        src={URL.createObjectURL(formData.image)}
                        alt="New image preview"
                        style={{ maxWidth: '200px', border: '2px solid green' }}
                      />
                    </div>
                  ) : (
                    <div className="current-image-display">
                      <p>Current image:</p>
                      <img
                        src={formData.image_path && formData.image_path !== 'null' ? 
                          `${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/documents/uploads/${formData.image_path.replace(/^uploads[\\/]/, '').replace(/\\/g, '/')}` : 
                          `${process.env.PUBLIC_URL}/placeholder-document.png`
                        }
                        alt="Current document"
                        style={{ maxWidth: '200px', border: '1px solid #ccc' }}
                        onError={(e) => {
                          e.target.onError = null; // Prevent infinite loops
                          console.log('IMAGE DEBUG: Failed to load image, using placeholder. Path was:', formData.image_path);
                          e.target.src = `${process.env.PUBLIC_URL}/placeholder-document.png`;
                        }}
                        onLoad={() => {
                          console.log('IMAGE DEBUG: Successfully loaded current image');
                        }}
                      />
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>  

          {/* Form Actions */}
          <div
            className="form-actions transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderTopColor: colors.border
            }}
          >
            <button
              type="button"
              className="btn-secondary transition-colors duration-200"
              onClick={onCancel}
              style={{
                backgroundColor: colors.surface,
                color: colors.text,
                borderColor: colors.border
              }}
            >
              {t('common.cancel') || 'Cancel'}
            </button>

            <button
              type="submit"
              className="btn-primary transition-colors duration-200"
              disabled={loading}
              style={{
                backgroundColor: colors.primary,
                color: 'white',
                borderColor: colors.primary,
                opacity: loading ? 0.6 : 1
              }}
            >
              {loading ?
                (t('common.saving') || 'Saving...') :
                (isEditing ?
                  (t('documents.updateDocument') || 'Update Document') :
                  (t('documents.addDocument') || 'Add Document')
                )
              }
            </button>
          </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default DocumentForm;
