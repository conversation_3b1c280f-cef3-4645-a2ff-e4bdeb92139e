import React, { useState, useEffect, useCallback } from 'react';
import { getDocuments, deleteDocument } from '../../services/documentService';
import ReserveButton from '../member/ReserveButton';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import '../../styles/Documents.css';

const DocumentList = ({ onEdit, onView, refreshTrigger }) => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalDocuments, setTotalDocuments] = useState(0);
  const [showPageJump, setShowPageJump] = useState(false);
  const [jumpToPage, setJumpToPage] = useState('');
  const [filters, setFilters] = useState({
    search: '',
    type: '',
    category: ''
  });

  // Theme and Language hooks
  const { getCurrentColors } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isLibrarian = user.role === 'librarian' || user.role === 'manager';
  const isMember = user.role === 'member';

  const fetchDocuments = useCallback(async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        per_page: 3, // Changed from 10 to 3 documents per page
        ...filters
      };

      const response = await getDocuments(params);
      setDocuments(response.documents);
      setTotalPages(response.pages);
      setTotalDocuments(response.total || response.pages * 3); // Estimate total if not provided
      setError('');
      console.log('PAGINATION DEBUG: Fetched page', currentPage, 'with', response.documents.length, 'documents. Total pages:', response.pages, 'Total documents:', response.total);
    } catch (err) {
      setError(err.message || 'Failed to fetch documents');
    } finally {
      setLoading(false);
    }
  }, [currentPage, filters]);

  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  // Refresh when refreshTrigger changes
  useEffect(() => {
    if (refreshTrigger > 0) {
      fetchDocuments();
    }
  }, [refreshTrigger, fetchDocuments]);

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchDocuments();
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDelete = async (documentId) => {
    const document = documents.find(doc => doc.id === documentId);
    const documentTitle = document ? document.title : `Document #${documentId}`;

    if (!window.confirm(`Are you sure you want to delete "${documentTitle}"?\n\nThis action cannot be undone.`)) {
      return;
    }

    try {
      console.log('DELETE DEBUG: Attempting to delete document ID:', documentId);
      await deleteDocument(documentId);
      console.log('DELETE DEBUG: Document deleted successfully');

      // Show success message
      alert(`Document "${documentTitle}" has been deleted successfully.`);

      // Refresh the list
      fetchDocuments();
    } catch (err) {
      console.error('DELETE DEBUG: Error deleting document:', err);

      // Handle specific error types
      if (err.response && err.response.status === 409) {
        setError('Cannot delete this document because it has related records. Please contact the administrator.');
      } else if (err.response && err.response.status === 404) {
        setError('Document not found. It may have already been deleted.');
        fetchDocuments(); // Refresh to remove from list
      } else {
        setError(err.message || 'Failed to delete document. Please try again.');
      }
    }
  };

  const handlePageJump = () => {
    const pageNum = parseInt(jumpToPage);
    if (pageNum >= 1 && pageNum <= totalPages) {
      setCurrentPage(pageNum);
      setShowPageJump(false);
      setJumpToPage('');
    }
  };

  const getDocumentTypeIcon = (type) => {
    switch (type) {
      case 'book': return '📚';
      case 'periodical': return '📰';
      case 'article': return '📄';
      case 'video': return '🎥';
      default: return '📄';
    }
  };

  if (loading) {
    return (
      <div
        className="loading transition-colors duration-200"
        style={{ color: colors.text }}
      >
        {t('common.loading') || 'Loading documents...'}
      </div>
    );
  }

  return (
    <div
      className="document-list transition-colors duration-200"
      style={{ backgroundColor: colors.background, color: colors.text }}
    >
      <div
        className="document-list-header transition-colors duration-200"
        style={{ backgroundColor: colors.surface, borderBottomColor: colors.border }}
      >
        <h2 style={{ color: colors.text }}>
          {t('documents.library') || 'Document Library'}
        </h2>

        {/* Search and Filters */}
        <form onSubmit={handleSearch} className="search-form">
          <div className="search-row">
            <input
              type="text"
              name="search"
              placeholder={t('documents.searchPlaceholder') || 'Search documents...'}
              value={filters.search}
              onChange={handleFilterChange}
              className="search-input transition-colors duration-200"
              style={{
                backgroundColor: colors.background,
                borderColor: colors.border,
                color: colors.text
              }}
            />

            <select
              name="type"
              value={filters.type}
              onChange={handleFilterChange}
              className="filter-select transition-colors duration-200"
              style={{
                backgroundColor: colors.background,
                borderColor: colors.border,
                color: colors.text
              }}
            >
              <option value="">{t('documents.allTypes') || 'All Types'}</option>
              <option value="book">{t('documents.books') || 'Books'}</option>
              <option value="periodical">{t('documents.periodicals') || 'Periodicals'}</option>
              <option value="article">{t('documents.articles') || 'Articles'}</option>
              <option value="video">{t('documents.videos') || 'Videos'}</option>
            </select>

            <input
              type="text"
              name="category"
              placeholder={t('documents.category') || 'Category'}
              value={filters.category}
              onChange={handleFilterChange}
              className="filter-input transition-colors duration-200"
              style={{
                backgroundColor: colors.background,
                borderColor: colors.border,
                color: colors.text
              }}
            />

            <button
              type="submit"
              className="search-btn transition-colors duration-200"
              style={{ backgroundColor: colors.primary, color: 'white' }}
            >
              {t('common.search') || 'Search'}
            </button>
          </div>
        </form>
      </div>

      {error && (
        <div
          className="error-message transition-colors duration-200"
          style={{
            backgroundColor: colors.error + '20',
            color: colors.error,
            borderColor: colors.error
          }}
        >
          {error}
        </div>
      )}

      {/* Documents Grid */}
      <div className="documents-grid">
        {documents.length === 0 ? (
          <div
            className="no-documents transition-colors duration-200"
            style={{ color: colors.textSecondary }}
          >
            <p>{t('documents.noDocuments') || 'No documents found.'}</p>
          </div>
        ) : (
          documents.map(document => (
            <div
              key={document.id}
              className="document-card transition-colors duration-200"
              data-document-id={document.id}
              style={{
                backgroundColor: colors.surface,
                borderColor: colors.border,
                boxShadow: `0 2px 8px ${colors.shadow}`
              }}
            >
              {/* Image Preview */}
              <div className="document-image">
                {document.image_path && document.image_path !== 'null' ? (
                  <img
                    src={`http://localhost:5005/uploads/${document.image_path.replace(/^uploads[\\/]/, '').replace(/\\/g, '/')}?t=${Date.now()}`}
                    alt={document.title}
                    onError={e => {
                      // Prevent infinite error loops
                      e.target.onError = null;
                      console.log('IMAGE DEBUG: Image load failed for:', document.image_path);
                      e.target.src = `${process.env.PUBLIC_URL}/placeholder-document.png`;
                    }}
                    onLoad={() => {
                      console.log('IMAGE DEBUG: Successfully loaded image for document:', document.id);
                    }}
                  />
                ) : (
                  <img
                    src={`${process.env.PUBLIC_URL}/placeholder-document.png`}
                    alt="Placeholder"
                  />
                )}
              </div>

              {/* Document Header */}
              <div
                className="document-header transition-colors duration-200"
                style={{ borderBottomColor: colors.border }}
              >
                <span className="document-type-icon">{getDocumentTypeIcon(document.document_type)}</span>
                <span
                  className="document-type transition-colors duration-200"
                  style={{ color: colors.textSecondary }}
                >
                  {document.document_type}
                </span>
                <span
                  className={`availability ${document.available_copies > 0 ? 'available' : 'unavailable'} transition-colors duration-200`}
                  style={{
                    color: document.available_copies > 0 ? colors.success : colors.error,
                    backgroundColor: document.available_copies > 0 ? colors.success + '20' : colors.error + '20'
                  }}
                >
                  {document.available_copies > 0 ?
                    (t('documents.available') || 'Available') :
                    (t('documents.unavailable') || 'Unavailable')
                  }
                </span>
              </div>

              {/* Document Content */}
              <div className="document-content">
                <h3
                  className="document-title transition-colors duration-200"
                  style={{ color: colors.text }}
                >
                  {document.title}
                </h3>
                {document.author && (
                  <p
                    className="document-author transition-colors duration-200"
                    style={{ color: colors.textSecondary }}
                  >
                    {t('documents.by') || 'by'} {document.author}
                  </p>
                )}
                <p
                  className="document-description transition-colors duration-200"
                  style={{ color: colors.textSecondary }}
                >
                  {document.description ?
                    (document.description.length > 100 ?
                      document.description.substring(0, 100) + '...' :
                      document.description
                    ) :
                    (t('documents.noDescription') || 'No description available')
                  }
                </p>

                <div
                  className="document-meta transition-colors duration-200"
                  style={{ color: colors.textSecondary }}
                >
                  {document.category && (
                    <span>
                      <strong style={{ color: colors.text }}>
                        {t('documents.category') || 'Category'}:
                      </strong> {document.category}
                    </span>
                  )}
                  {document.publisher && (
                    <span>
                      <strong style={{ color: colors.text }}>
                        {t('documents.publisher') || 'Publisher'}:
                      </strong> {document.publisher}
                    </span>
                  )}
                  <span>
                    <strong style={{ color: colors.text }}>
                      {t('documents.copies') || 'Copies'}:
                    </strong> {document.available_copies}/{document.total_copies}
                  </span>
                </div>
              </div>

              {/* Document Actions */}
              <div
                className="document-actions transition-colors duration-200"
                style={{ borderTopColor: colors.border }}
              >
                <button
                  onClick={() => onView(document)}
                  className="btn-secondary transition-colors duration-200"
                  style={{
                    backgroundColor: colors.background,
                    borderColor: colors.border,
                    color: colors.text
                  }}
                >
                  {t('common.view') || 'View'}
                </button>
                {isMember && (
                  <ReserveButton
                    document={document}
                    onReservationSuccess={() => fetchDocuments()}
                    className="btn-reserve"
                  />
                )}
                {isLibrarian && (
                  <>
                    <button
                      onClick={() => onEdit(document)}
                      className="btn-primary transition-colors duration-200"
                      style={{ backgroundColor: colors.primary, color: 'white' }}
                    >
                      {t('common.edit') || 'Edit'}
                    </button>
                    <button
                      onClick={() => handleDelete(document.id)}
                      className="btn-danger transition-colors duration-200"
                      style={{ backgroundColor: colors.error, color: 'white' }}
                    >
                      {t('common.delete') || 'Delete'}
                    </button>
                  </>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Enhanced Pagination */}
      {totalPages > 1 && (
        <div
          className="pagination transition-colors duration-200"
          style={{
            backgroundColor: colors.surface,
            borderColor: colors.border
          }}
        >
          <button
            onClick={() => setCurrentPage(1)}
            disabled={currentPage === 1}
            className="pagination-btn transition-colors duration-200"
            title="First Page"
            style={{
              backgroundColor: currentPage === 1 ? colors.background : colors.surface,
              color: currentPage === 1 ? colors.textSecondary : colors.text,
              borderColor: colors.border
            }}
          >
            ⏮️ {t('pagination.first') || 'First'}
          </button>

          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="pagination-btn transition-colors duration-200"
            title="Previous Page"
            style={{
              backgroundColor: currentPage === 1 ? colors.background : colors.surface,
              color: currentPage === 1 ? colors.textSecondary : colors.text,
              borderColor: colors.border
            }}
          >
            ⬅️ {t('pagination.previous') || 'Previous'}
          </button>

          <div className="pagination-info">
            {!showPageJump ? (
              <>
                <span
                  className="page-numbers clickable transition-colors duration-200"
                  onClick={() => setShowPageJump(true)}
                  title="Click to jump to a specific page"
                  style={{ color: colors.text }}
                >
                  {t('pagination.page') || 'Page'} <strong>{currentPage}</strong> {t('pagination.of') || 'of'} <strong>{totalPages}</strong>
                </span>
                <span
                  className="items-info transition-colors duration-200"
                  style={{ color: colors.textSecondary }}
                >
                  {t('pagination.showing') || 'Showing'} {((currentPage - 1) * 3) + 1}-{((currentPage - 1) * 3) + documents.length} {t('pagination.of') || 'of'} {totalDocuments} {t('documents.title') || 'documents'}
                </span>
              </>
            ) : (
              <div className="page-jump">
                <span style={{ color: colors.text }}>
                  {t('pagination.goToPage') || 'Go to page'}:
                </span>
                <input
                  type="number"
                  min="1"
                  max={totalPages}
                  value={jumpToPage}
                  onChange={(e) => setJumpToPage(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handlePageJump()}
                  className="page-jump-input transition-colors duration-200"
                  autoFocus
                  style={{
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border
                  }}
                />
                <button
                  onClick={handlePageJump}
                  className="page-jump-btn transition-colors duration-200"
                  style={{
                    backgroundColor: colors.primary,
                    color: 'white'
                  }}
                >
                  {t('common.go') || 'Go'}
                </button>
                <button
                  onClick={() => {setShowPageJump(false); setJumpToPage('');}}
                  className="page-jump-cancel transition-colors duration-200"
                  style={{
                    backgroundColor: colors.textSecondary,
                    color: 'white'
                  }}
                >
                  ✕
                </button>
              </div>
            )}
          </div>

          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="pagination-btn transition-colors duration-200"
            title="Next Page"
            style={{
              backgroundColor: currentPage === totalPages ? colors.background : colors.surface,
              color: currentPage === totalPages ? colors.textSecondary : colors.text,
              borderColor: colors.border
            }}
          >
            {t('pagination.next') || 'Next'} ➡️
          </button>

          <button
            onClick={() => setCurrentPage(totalPages)}
            disabled={currentPage === totalPages}
            className="pagination-btn transition-colors duration-200"
            title="Last Page"
            style={{
              backgroundColor: currentPage === totalPages ? colors.background : colors.surface,
              color: currentPage === totalPages ? colors.textSecondary : colors.text,
              borderColor: colors.border
            }}
          >
            {t('pagination.last') || 'Last'} ⏭️
          </button>
        </div>
      )}

      {/* Show pagination info even with single page */}
      {totalPages <= 1 && documents.length > 0 && (
        <div
          className="pagination-info single-page transition-colors duration-200"
          style={{
            backgroundColor: colors.surface,
            borderColor: colors.border
          }}
        >
          <span
            className="items-info transition-colors duration-200"
            style={{ color: colors.textSecondary }}
          >
            {t('pagination.showing') || 'Showing'} {documents.length} {t('documents.title') || 'document'}{documents.length !== 1 ? 's' : ''}
          </span>
        </div>
      )}
    </div>
  );
};

export default DocumentList;
