const translations = {
  // Común
  common: {
    loading: 'Cargando...',
    error: 'Error',
    success: 'Éxito',
    cancel: 'Cancelar',
    confirm: 'Confirmar',
    save: '<PERSON>ar',
    edit: 'Editar',
    delete: 'Eliminar',
    search: '<PERSON><PERSON>',
    filter: 'Filtrar',
    sort: 'Ordenar',
    refresh: 'Actualizar',
    back: 'Atrás',
    next: 'Siguient<PERSON>',
    previous: 'Anterior',
    close: 'Cerrar',
    open: 'Abrir',
    yes: 'Sí',
    no: 'No',
    ok: 'OK',
    apply: 'Aplicar',
    reset: 'Restablecer',
    clear: 'Limpiar',
    view: 'Ver'
  },

  // Navegación
  nav: {
    home: 'Inicio',
    dashboard: 'Panel de control',
    books: 'Libro<PERSON>',
    members: 'Miembros',
    reports: 'Informes',
    settings: 'Configuración',
    profile: 'Perfil',
    logout: 'Cerrar sesión',
    login: 'Iniciar sesión',
    register: 'Registrarse',
    analytics: 'Analíticas'
  },

  // Autenticación
  auth: {
    login: 'Iniciar sesión',
    register: 'Registrar<PERSON>',
    logout: 'Cerra<PERSON> sesión',
    username: 'Nombre de usuario',
    password: '<PERSON><PERSON><PERSON>ña',
    email: 'Correo electrónico',
    confirmPassword: 'Confirmar contraseña',
    forgotPassword: '¿Olvidaste tu contraseña?',
    rememberMe: 'Recordarme',
    loginSuccess: 'Inicio de sesión exitoso',
    loginError: 'Credenciales inválidas',
    registerSuccess: 'Registro exitoso',
    registerError: 'Error en el registro',
    logoutSuccess: 'Sesión cerrada exitosamente',
    invalidCredentials: 'Nombre de usuario o contraseña inválidos',
    accountCreated: 'Cuenta creada exitosamente',
    passwordMismatch: 'Las contraseñas no coinciden',
    emailRequired: 'El correo electrónico es requerido',
    usernameRequired: 'El nombre de usuario es requerido',
    passwordRequired: 'La contraseña es requerida'
  },

  // Sección Héroe
  hero: {
    title1: 'Buscar y reseñar',
    title2: 'tu',
    title3: 'libro favorito',
    title4: 'sin esfuerzo',
    description: '¡Embárcate en un viaje literario como nunca antes con nuestra aplicación de biblioteca revolucionaria! Presentamos una experiencia fluida que trasciende las fronteras tradicionales, donde puedes buscar sin esfuerzo tus libros favoritos.✨',
    startButton: 'Comenzar ahora'
  },

  // Sección Características
  features: {
    title: 'CARACTERÍSTICAS',
    subtitle: '¿Qué Puedes Hacer?',
    searchBook: 'Buscar libro',
    searchBookDesc: 'Encuentra sin esfuerzo tu próxima lectura con nuestra búsqueda de libros potente e intuitiva.',
    reviewBook: 'Reseñar libro',
    reviewBookDesc: 'Descubre críticas perspicaces y comparte tus pensamientos sobre diversas obras maestras literarias sin esfuerzo.',
    wishlistBook: 'Lista de deseos',
    wishlistBookDesc: 'Organiza tus sueños literarios: agrega libros a tu lista de deseos para futuras aventuras y descubrimientos.'
  },

  // Sección Servicios
  services: {
    title: 'SERVICIOS',
    subtitle: 'Los Servicios Para Ti',
    rent: 'Alquilar',
    favoriteBook: 'tu libro favorito',
    fairlyEasy: 'bastante fácil en',
    description1: 'Ver, alquilar y organizar tus libros favoritos nunca ha sido más fácil. Un alquiler de biblioteca digital integrado que es simple de usar, BiblioSmart te permite pasar menos tiempo gestionando tu trabajo y más tiempo haciéndolo realmente!',
    description2: 'Alquileres sin esfuerzo, estantes personalizados—BiblioSmart transforma la gestión de libros, mejorando tu experiencia de lectura~'
  },

  // Sección Alquileres Rápidos
  quickRentals: {
    title: 'Alquileres Rápidos de Libros:',
    dive: 'Sumérgete',
    into: 'en',
    readingInstantly: 'la Lectura Instantáneamente',
    description1: 'Descubre el deleite literario instantáneo. Accede a una vasta biblioteca, toma prestadas tus lecturas favoritas y sumérgete en historias cautivadoras en minutos. ¡Lectura rápida y fácil, a solo un clic!',
    description2: 'Desbloquea un mundo de historias sin esfuerzo. Navega géneros, elige, alquila en minutos. Gestiona sin problemas tus aventuras de lectura con nuestra plataforma intuitiva~'
  },

  // Sección Reseñas
  reviews: {
    title: 'RESEÑAS',
    subtitle: 'Reseñas de Otros'
  },

  // Sección Ubicación
  location: {
    title: 'UBICACIÓN',
    subtitle: 'Ubicación de Nuestra Biblioteca'
  },

  // Sección Pie de Página
  footer: {
    managedBy: 'Gestionado Por',
    socialMedia: 'Redes Sociales',
    slogan: 'Eslogan',
    hashtag: '#AlquilarLibrosFavoritos',
    copyright: '© 2024 BiblioSmart. Todos los derechos reservados.'
  },

  // Panel de control
  dashboard: {
    title: 'Panel de control',
    overview: 'Resumen',
    statistics: 'Estadísticas',
    recentActivity: 'Actividad reciente',
    quickActions: 'Acciones rápidas',
    totalBooks: 'Préstamos activos',
    availableBooks: 'Libros disponibles',
    borrowedBooks: 'Libros prestados',
    totalMembers: 'Total de miembros',
    activeMembers: 'Miembros activos',
    overdueBooks: 'Artículos vencidos',
    reservations: 'Reservas activas',
    notifications: 'Notificaciones',
    activeBorrowings: 'Préstamos activos',
    activeReservations: 'Reservas activas',
    overdueItems: 'Artículos vencidos',
    totalFines: 'Total de multas'
  },

  // Libros
  books: {
    title: 'Libros',
    addBook: 'Agregar libro',
    editBook: 'Editar libro',
    deleteBook: 'Eliminar libro',
    bookTitle: 'Título del libro',
    author: 'Autor',
    isbn: 'ISBN',
    category: 'Categoría',
    subject: 'Materia',
    publisher: 'Editorial',
    publishedDate: 'Fecha de publicación',
    pages: 'Páginas',
    language: 'Idioma',
    description: 'Descripción',
    totalCopies: 'Copias totales',
    availableCopies: 'Copias disponibles',
    location: 'Ubicación',
    status: 'Estado',
    available: 'Disponible',
    borrowed: 'Prestado',
    reserved: 'Reservado',
    maintenance: 'Mantenimiento',
    searchBooks: 'Buscar libros',
    filterByCategory: 'Filtrar por categoría',
    sortBy: 'Ordenar por',
    bookAdded: 'Libro agregado exitosamente',
    bookUpdated: 'Libro actualizado exitosamente',
    bookDeleted: 'Libro eliminado exitosamente',
    bookNotFound: 'Libro no encontrado',
    noBooks: 'No se encontraron libros'
  },

  // Documentos
  documents: {
    title: 'Documentos',
    addDocument: 'Agregar documento',
    editDocument: 'Editar documento',
    updateDocument: 'Actualizar documento',
    deleteDocument: 'Eliminar documento',
    library: 'Biblioteca de documentos',
    management: 'Gestión de documentos',
    searchPlaceholder: 'Buscar documentos...',
    allTypes: 'Todos los tipos',
    books: 'Libros',
    periodicals: 'Publicaciones periódicas',
    articles: 'Artículos',
    videos: 'Videos',
    by: 'por',
    noDescription: 'No hay descripción disponible',
    copies: 'Copias',
    available: 'Disponible',
    unavailable: 'No disponible',
    category: 'Categoría',
    publisher: 'Editorial',
    noDocuments: 'No se encontraron documentos',
    // Form fields
    title: 'Título',
    description: 'Descripción',
    documentType: 'Tipo de documento',
    barcode: 'Código de barras',
    basicInformation: 'Información básica',
    // Detail view
    quickInformation: 'Información rápida',
    type: 'Tipo',
    copies: 'Copias',
    noImageAvailable: 'No hay imagen disponible',
    reserveDocument: 'Reservar documento',
    // Form sections
    classification: 'Clasificación',
    keywords: 'Palabras clave',
    subject: 'Materia',
    bookInformation: 'Información del libro',
    periodicalInformation: 'Información de publicación periódica',
    articleInformation: 'Información del artículo',
    videoInformation: 'Información del video',
    author: 'Autor',
    edition: 'Edición',
    series: 'Serie',
    frequency: 'Frecuencia',
    volume: 'Volumen',
    issue: 'Número',
    journal: 'Revista',
    director: 'Director',
    duration: 'Duración',
    resolution: 'Resolución',
    publicationDetails: 'Detalles de publicación',
    publicationDate: 'Fecha de publicación',
    language: 'Idioma',
    physicalDetails: 'Detalles físicos',
    pages: 'Páginas',
    format: 'Formato',
    shelfLocation: 'Ubicación en estante',
    availability: 'Disponibilidad',
    totalCopies: 'Copias totales',
    availableCopies: 'Copias disponibles',
    image: 'Imagen',
    documentImage: 'Imagen del documento',
    // Detail view additional
    location: 'Ubicación',
    metadata: 'Metadatos',
    added: 'Agregado',
    lastUpdated: 'Última actualización',
    addedBy: 'Agregado por'
  },

  // Paginación
  pagination: {
    first: 'Primero',
    previous: 'Anterior',
    next: 'Siguiente',
    last: 'Último',
    page: 'Página',
    of: 'de',
    showing: 'Mostrando',
    goToPage: 'Ir a la página'
  },

  // Miembros
  members: {
    title: 'Miembros',
    addMember: 'Agregar miembro',
    editMember: 'Editar miembro',
    deleteMember: 'Eliminar miembro',
    memberName: 'Nombre del miembro',
    memberType: 'Tipo de miembro',
    studentId: 'ID de estudiante',
    phone: 'Teléfono',
    address: 'Dirección',
    joinDate: 'Fecha de ingreso',
    status: 'Estado',
    active: 'Activo',
    inactive: 'Inactivo',
    suspended: 'Suspendido',
    borrowingHistory: 'Historial de préstamos',
    currentBorrowings: 'Préstamos actuales',
    reservations: 'Reservas',
    fines: 'Multas',
    memberAdded: 'Miembro agregado exitosamente',
    memberUpdated: 'Miembro actualizado exitosamente',
    memberDeleted: 'Miembro eliminado exitosamente',
    memberNotFound: 'Miembro no encontrado',
    noMembers: 'No se encontraron miembros'
  },

  // Préstamos
  borrowing: {
    title: 'Préstamos',
    borrowBook: 'Prestar libro',
    returnBook: 'Devolver libro',
    renewBook: 'Renovar libro',
    borrowDate: 'Fecha de préstamo',
    dueDate: 'Fecha de vencimiento',
    returnDate: 'Fecha de devolución',
    renewalCount: 'Número de renovaciones',
    fine: 'Multa',
    overdue: 'Vencido',
    returned: 'Devuelto',
    borrowed: 'Prestado',
    myBorrowings: 'Mis préstamos',
    borrowingHistory: 'Historial de préstamos',
    bookBorrowed: 'Libro prestado exitosamente',
    bookReturned: 'Libro devuelto exitosamente',
    bookRenewed: 'Libro renovado exitosamente',
    borrowingFailed: 'Error al prestar el libro',
    returnFailed: 'Error al devolver el libro',
    renewalFailed: 'Error al renovar el libro',
    maxRenewalsReached: 'Máximo de renovaciones alcanzado',
    bookOverdue: 'Libro vencido',
    noBorrowings: 'No se encontraron préstamos'
  },

  // Reservas
  reservations: {
    title: 'Reservas',
    reserveBook: 'Reservar libro',
    cancelReservation: 'Cancelar reserva',
    reservationDate: 'Fecha de reserva',
    expiryDate: 'Fecha de expiración',
    status: 'Estado',
    pending: 'Pendiente',
    ready: 'Listo',
    expired: 'Expirado',
    cancelled: 'Cancelado',
    myReservations: 'Mis reservas',
    bookReserved: 'Libro reservado exitosamente',
    reservationCancelled: 'Reserva cancelada exitosamente',
    reservationFailed: 'Error al reservar el libro',
    reservationExpired: 'La reserva ha expirado',
    noReservations: 'No se encontraron reservas'
  },

  // Características de IA
  ai: {
    title: 'Características de IA',
    recommendations: 'Recomendaciones de IA',
    chatAssistant: 'Asistente de chat de IA',
    smartSearch: 'Búsqueda inteligente',
    trending: 'Libros en tendencia',
    forYou: 'Para ti',
    basedOnHistory: 'Basado en tu historial de lectura y preferencias',
    popularBooks: 'Libros más populares en los últimos 30 días',
    whyRecommended: 'Por qué se recomienda:',
    matchesCategory: 'Coincide con tu categoría favorita: {{category}}',
    matchesSubject: 'Coincide con tu interés en: {{subject}}',
    favoriteAuthor: 'Por tu autor favorito: {{author}}',
    aiAssistantWelcome: '¡Hola! Soy tu asistente de biblioteca de IA. Puedo ayudarte a encontrar libros, responder preguntas sobre las políticas de la biblioteca y asistirte con tus préstamos y reservas. ¿Cómo puedo ayudarte hoy?',
    quickActions: 'Acciones rápidas:',
    findProgrammingBooks: 'Encontrar libros sobre programación',
    checkBorrowings: 'Verificar mis préstamos actuales',
    renewBook: '¿Cómo renuevo un libro?',
    libraryPolicies: '¿Cuáles son las políticas de la biblioteca?',
    recommendBooks: 'Recomiéndame libros',
    thinking: 'Pensando...',
    typeMessage: 'Pregúntame cualquier cosa sobre la biblioteca...',
    noRecommendations: '¡Comienza a prestar libros para obtener recomendaciones personalizadas!',
    noTrendingData: 'No hay datos de tendencias disponibles aún.',
    aiError: 'Lo siento, encontré un error. Por favor, inténtalo de nuevo.',
    connectionError: 'Lo siento, tengo problemas de conexión ahora mismo. Por favor, inténtalo más tarde.'
  },

  // Configuración
  settings: {
    title: 'Configuración',
    general: 'General',
    appearance: 'Apariencia',
    language: 'Idioma',
    notifications: 'Notificaciones',
    privacy: 'Privacidad',
    security: 'Seguridad',
    account: 'Cuenta',
    darkMode: 'Modo oscuro',
    lightMode: 'Modo claro',
    theme: 'Tema',
    changeLanguage: 'Cambiar idioma',
    selectLanguage: 'Seleccionar idioma',
    emailNotifications: 'Notificaciones por correo',
    pushNotifications: 'Notificaciones push',
    smsNotifications: 'Notificaciones SMS',
    changePassword: 'Cambiar contraseña',
    currentPassword: 'Contraseña actual',
    newPassword: 'Nueva contraseña',
    confirmNewPassword: 'Confirmar nueva contraseña',
    passwordChanged: 'Contraseña cambiada exitosamente',
    settingsSaved: 'Configuración guardada exitosamente'
  },

  // Notificaciones
  notifications: {
    title: 'Notificaciones',
    markAsRead: 'Marcar como leído',
    markAllAsRead: 'Marcar todo como leído',
    deleteNotification: 'Eliminar notificación',
    noNotifications: 'No hay notificaciones',
    bookDueSoon: 'Libro próximo a vencer: {{title}}',
    bookOverdue: 'Libro vencido: {{title}}',
    bookReady: 'Libro reservado listo: {{title}}',
    reservationExpiring: 'Reserva expirando: {{title}}',
    newBookAvailable: 'Nuevo libro disponible: {{title}}',
    accountSuspended: 'Tu cuenta ha sido suspendida',
    fineAdded: 'Multa agregada: ${{amount}}',
    reminderDue: 'Recordatorio: Libro vence mañana',
    notificationRead: 'Notificación marcada como leída',
    allNotificationsRead: 'Todas las notificaciones marcadas como leídas'
  },

  // Errores
  errors: {
    general: 'Ocurrió un error',
    networkError: 'Error de red. Por favor, verifica tu conexión.',
    serverError: 'Error del servidor. Por favor, inténtalo más tarde.',
    notFound: 'Recurso no encontrado',
    unauthorized: 'No estás autorizado para realizar esta acción',
    forbidden: 'Acceso prohibido',
    validationError: 'Por favor, verifica tu entrada',
    sessionExpired: 'Tu sesión ha expirado. Por favor, inicia sesión de nuevo.',
    fileUploadError: 'Error al subir el archivo',
    fileSizeError: 'Tamaño de archivo demasiado grande',
    fileTypeError: 'Tipo de archivo inválido'
  },

  // Mensajes de éxito
  success: {
    operationCompleted: 'Operación completada exitosamente',
    dataSaved: 'Datos guardados exitosamente',
    dataUpdated: 'Datos actualizados exitosamente',
    dataDeleted: 'Datos eliminados exitosamente',
    emailSent: 'Correo enviado exitosamente',
    passwordReset: 'Contraseña restablecida exitosamente',
    profileUpdated: 'Perfil actualizado exitosamente',
    settingsUpdated: 'Configuración actualizada exitosamente'
  }
};

export default translations;
