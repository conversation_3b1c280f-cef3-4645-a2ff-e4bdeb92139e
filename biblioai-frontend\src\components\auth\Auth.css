/* Auth.css */
/* Styles for authentication pages only */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  background-image: url('https://images.unsplash.com/photo-1507842217343-583bb7270b66?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80');
  background-size: cover;
  background-position: center;
  /* The position:fixed affects scrolling only on auth pages */
  position: fixed;
  top: 0;
  left: 0;
  padding: 0;
  z-index: 1000;
}

.auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(36, 62, 85, 0.85);
}

/* Default single-column card (for Login) */
.auth-card {
  position: relative;
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 450px; /* Narrower for login */
  margin: 0;
  z-index: 1;
}

/* Multi-column card (for Register only) */
.auth-card.register {
  max-width: 800px; /* Wider for register */
}

.auth-card.register form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.auth-card.register .form-group {
  margin-bottom: 0;
}

.auth-card.register .form-group.full-width {
  grid-column: span 2;
}

/* Common styles for both forms */
.auth-card h2 {
  color: #243e55;
  text-align: center;
  margin: 0 0 1.5rem 0;
  font-size: 1.8rem;
  font-weight: 600;
  font-family: 'Georgia', serif;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #243e55;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus {
  border-color: #4a6fa5;
  outline: none;
  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.2);
}

.btn-primary {
  width: 100%;
  padding: 0.75rem;
  background-color: #4a6fa5;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  /* margin-top: 0.5rem; */
}

/* For register form only */
.auth-card.register .btn-primary {
  grid-column: span 2;
}

.btn-primary:hover {
  background-color: #3a5a80;
}

.btn-primary:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.alert-error {
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 5px;
  text-align: center;
}

/* For register form only */
.auth-card.register .alert-error {
  grid-column: span 2;
}

.auth-link {
  text-align: center;
  margin-top: 1rem;
  color: #666;
}

/* For register form only */
.auth-card.register .auth-link {
  grid-column: span 2;
}

.auth-link a {
  color: #4a6fa5;
  text-decoration: none;
  font-weight: 500;
}

.auth-link a:hover {
  text-decoration: underline;
}

/* Responsive adjustments for register form */
@media (max-width: 768px) {
  .auth-card.register form {
    grid-template-columns: 1fr;
  }
  
  .auth-card.register .form-group.full-width,
  .auth-card.register .btn-primary,
  .auth-card.register .alert-error,
  .auth-card.register .auth-link {
    grid-column: span 1;
  }
}

@media (max-height: 700px) {
  .auth-card {
    padding: 1.5rem;
    max-height: 95vh;
    overflow-y: auto;
  }
}

/* DARK MODE STYLES */
html.dark .auth-container::before {
  background: rgba(15, 23, 42, 0.9);
}

html.dark .auth-card {
  background: #1e293b;
  color: #f1f5f9;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.5);
}

html.dark .auth-card h2 {
  color: #f1f5f9;
}

html.dark .form-group label {
  color: #f1f5f9;
}

html.dark .form-group input,
html.dark .form-group select {
  background-color: #334155;
  border-color: #475569;
  color: #f1f5f9;
}

html.dark .form-group input:focus,
html.dark .form-group select:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
}

html.dark .form-group input::placeholder {
  color: #94a3b8;
}

html.dark .btn-primary {
  background-color: #60a5fa;
}

html.dark .btn-primary:hover {
  background-color: #3b82f6;
}

html.dark .btn-primary:disabled {
  background-color: #475569;
  color: #94a3b8;
}

html.dark .alert-error {
  background-color: #7f1d1d;
  color: #fecaca;
  border-color: #991b1b;
}

html.dark .auth-link {
  color: #94a3b8;
}

html.dark .auth-link a {
  color: #60a5fa;
}

html.dark .auth-link a:hover {
  color: #93c5fd;
}

/* Theme toggle button for auth pages */
.auth-theme-toggle {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1001;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.auth-theme-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

html.dark .auth-theme-toggle {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

html.dark .auth-theme-toggle:hover {
  background: rgba(0, 0, 0, 0.5);
}