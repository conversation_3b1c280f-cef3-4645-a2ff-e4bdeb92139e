import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import '../../styles/Documents.css';

const DocumentDetail = ({ document, onClose, onEdit }) => {
  // Theme and Language hooks
  const { getCurrentColors } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();

  if (!document) return null;

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isLibrarian = user.role === 'librarian' || user.role === 'manager';

  const getDocumentTypeIcon = (type) => {
    switch (type) {
      case 'book': return '📚';
      case 'periodical': return '📰';
      case 'article': return '📄';
      case 'video': return '🎥';
      default: return '📄';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const renderTypeSpecificInfo = () => {
    switch (document.document_type) {
      case 'book':
        return (
          <div
            className="detail-section transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <h4 style={{ color: colors.text }}>
              {t('documents.bookInformation') || 'Book Information'}
            </h4>
            <div className="info-grid">
              {document.author && (
                <div className="info-item">
                  <label style={{ color: colors.textSecondary }}>
                    {t('documents.author') || 'Author'}:
                  </label>
                  <span style={{ color: colors.text }}>{document.author}</span>
                </div>
              )}
              {document.edition && (
                <div className="info-item">
                  <label style={{ color: colors.textSecondary }}>
                    {t('documents.edition') || 'Edition'}:
                  </label>
                  <span style={{ color: colors.text }}>{document.edition}</span>
                </div>
              )}
              {document.series && (
                <div className="info-item">
                  <label style={{ color: colors.textSecondary }}>
                    {t('documents.series') || 'Series'}:
                  </label>
                  <span style={{ color: colors.text }}>{document.series}</span>
                </div>
              )}
            </div>
          </div>
        );
      
      case 'periodical':
        return (
          <div
            className="detail-section transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <h4 style={{ color: colors.text }}>
              {t('documents.periodicalInformation') || 'Periodical Information'}
            </h4>
            <div className="info-grid">
              {document.issn && (
                <div className="info-item">
                  <label style={{ color: colors.textSecondary }}>ISSN:</label>
                  <span style={{ color: colors.text }}>{document.issn}</span>
                </div>
              )}
              {document.frequency && (
                <div className="info-item">
                  <label style={{ color: colors.textSecondary }}>
                    {t('documents.frequency') || 'Frequency'}:
                  </label>
                  <span style={{ color: colors.text }}>{document.frequency}</span>
                </div>
              )}
              {document.volume && (
                <div className="info-item">
                  <label style={{ color: colors.textSecondary }}>
                    {t('documents.volume') || 'Volume'}:
                  </label>
                  <span style={{ color: colors.text }}>{document.volume}</span>
                </div>
              )}
              {document.issue && (
                <div className="info-item">
                  <label style={{ color: colors.textSecondary }}>
                    {t('documents.issue') || 'Issue'}:
                  </label>
                  <span style={{ color: colors.text }}>{document.issue}</span>
                </div>
              )}
            </div>
          </div>
        );
      
      case 'article':
        return (
          <div
            className="detail-section transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <h4 style={{ color: colors.text }}>
              {t('documents.articleInformation') || 'Article Information'}
            </h4>
            <div className="info-grid">
              {document.author && (
                <div className="info-item">
                  <label style={{ color: colors.textSecondary }}>
                    {t('documents.author') || 'Author'}:
                  </label>
                  <span style={{ color: colors.text }}>{document.author}</span>
                </div>
              )}
              {document.journal && (
                <div className="info-item">
                  <label style={{ color: colors.textSecondary }}>
                    {t('documents.journal') || 'Journal'}:
                  </label>
                  <span style={{ color: colors.text }}>{document.journal}</span>
                </div>
              )}
              {document.doi && (
                <div className="info-item">
                  <label style={{ color: colors.textSecondary }}>DOI:</label>
                  <span style={{ color: colors.text }}>{document.doi}</span>
                </div>
              )}
            </div>
          </div>
        );
      
      case 'video':
        return (
          <div
            className="detail-section transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <h4 style={{ color: colors.text }}>
              {t('documents.videoInformation') || 'Video Information'}
            </h4>
            <div className="info-grid">
              {document.director && (
                <div className="info-item">
                  <label style={{ color: colors.textSecondary }}>
                    {t('documents.director') || 'Director'}:
                  </label>
                  <span style={{ color: colors.text }}>{document.director}</span>
                </div>
              )}
              {document.duration && (
                <div className="info-item">
                  <label style={{ color: colors.textSecondary }}>
                    {t('documents.duration') || 'Duration'}:
                  </label>
                  <span style={{ color: colors.text }}>{document.duration} minutes</span>
                </div>
              )}
              {document.resolution && (
                <div className="info-item">
                  <label style={{ color: colors.textSecondary }}>
                    {t('documents.resolution') || 'Resolution'}:
                  </label>
                  <span style={{ color: colors.text }}>{document.resolution}</span>
                </div>
              )}
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div
      className="document-detail-overlay"
      style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
    >
      <div
        className="document-detail transition-colors duration-200"
        style={{
          backgroundColor: colors.background,
          color: colors.text,
          border: `1px solid ${colors.border}`
        }}
      >
        <div
          className="document-detail-header transition-colors duration-200"
          style={{
            backgroundColor: colors.surface,
            borderBottomColor: colors.border
          }}
        >
          <div className="header-left">
            <span className="document-type-icon">
              {getDocumentTypeIcon(document.document_type)}
            </span>
            <div>
              <h2 style={{ color: colors.text }}>{document.title}</h2>
              <span
                className="document-type-label transition-colors duration-200"
                style={{
                  color: colors.textSecondary,
                  backgroundColor: colors.primary + '20',
                  borderColor: colors.primary
                }}
              >
                {document.document_type}
              </span>
            </div>
          </div>
          <div className="header-right">
            <div className="availability-status">
              {document.available_copies > 0 ? (
                <span
                  className="status available transition-colors duration-200"
                  style={{
                    color: colors.success,
                    backgroundColor: colors.success + '20'
                  }}
                >
                  ✓ {t('documents.available') || 'Available'} ({document.available_copies}/{document.total_copies})
                </span>
              ) : (
                <span
                  className="status unavailable transition-colors duration-200"
                  style={{
                    color: colors.error,
                    backgroundColor: colors.error + '20'
                  }}
                >
                  ✗ {t('documents.unavailable') || 'Unavailable'} (0/{document.total_copies})
                </span>
              )}
            </div>
            <button
              onClick={onClose}
              className="close-btn transition-colors duration-200"
              style={{
                color: colors.textSecondary,
                backgroundColor: 'transparent'
              }}
            >
              ×
            </button>
          </div>
        </div>

        <div
          className="document-detail-content transition-colors duration-200"
          style={{ backgroundColor: colors.background }}
        >
          <div className="content-layout">
            {/* Left Column - Image and Basic Info */}
            <div className="left-column">
              {/* Document Image */}
              <div
                className="document-image-section transition-colors duration-200"
                style={{
                  backgroundColor: colors.surface,
                  borderColor: colors.border
                }}
              >
                {document.image_path ? (
                  <img
                    src={`http://localhost:5005/uploads/${document.image_path.replace(/^uploads[\\/]/, '').replace(/\\/g, '/')}`}
                    alt={document.title}
                    className="document-image"
                    onError={(e) => {
                      e.target.onError = null;
                      e.target.src = `${process.env.PUBLIC_URL}/placeholder-document.png`;
                    }}
                  />
                ) : (
                  <div
                    className="no-image-placeholder transition-colors duration-200"
                    style={{
                      backgroundColor: colors.surface,
                      color: colors.textSecondary
                    }}
                  >
                    <span className="placeholder-icon">{getDocumentTypeIcon(document.document_type)}</span>
                    <span className="placeholder-text">
                      {t('documents.noImageAvailable') || 'No Image Available'}
                    </span>
                  </div>
                )}
              </div>

              {/* Quick Info Card */}
              <div
                className="quick-info-card transition-colors duration-200"
                style={{
                  backgroundColor: colors.surface,
                  borderColor: colors.border
                }}
              >
                <h4 style={{ color: colors.text }}>
                  {t('documents.quickInformation') || 'Quick Information'}
                </h4>
                <div className="quick-info-item">
                  <span
                    className="quick-label"
                    style={{ color: colors.textSecondary }}
                  >
                    {t('documents.type') || 'Type'}:
                  </span>
                  <span
                    className="quick-value"
                    style={{ color: colors.text }}
                  >
                    {document.document_type}
                  </span>
                </div>
                <div className="quick-info-item">
                  <span
                    className="quick-label"
                    style={{ color: colors.textSecondary }}
                  >
                    {t('documents.barcode') || 'Barcode'}:
                  </span>
                  <span
                    className="quick-value barcode"
                    style={{ color: colors.primary }}
                  >
                    {document.barcode}
                  </span>
                </div>
                <div className="quick-info-item">
                  <span
                    className="quick-label"
                    style={{ color: colors.textSecondary }}
                  >
                    {t('documents.copies') || 'Copies'}:
                  </span>
                  <span
                    className="quick-value"
                    style={{ color: colors.text }}
                  >
                    {document.available_copies}/{document.total_copies}
                  </span>
                </div>
                {document.category && (
                  <div className="quick-info-item">
                    <span
                      className="quick-label"
                      style={{ color: colors.textSecondary }}
                    >
                      {t('documents.category') || 'Category'}:
                    </span>
                    <span
                      className="quick-value"
                      style={{ color: colors.text }}
                    >
                      {document.category}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Right Column - Detailed Information */}
            <div className="right-column">
              {/* Description */}
              {document.description && (
                <div
                  className="description-section transition-colors duration-200"
                  style={{
                    backgroundColor: colors.surface,
                    borderColor: colors.border
                  }}
                >
                  <h4 style={{ color: colors.text }}>
                    {t('documents.description') || 'Description'}
                  </h4>
                  <p
                    className="description-text"
                    style={{ color: colors.text }}
                  >
                    {document.description}
                  </p>
                </div>
              )}

              {/* Basic Information */}
              <div
                className="detail-section transition-colors duration-200"
                style={{
                  backgroundColor: colors.surface,
                  borderColor: colors.border
                }}
              >
                <h4 style={{ color: colors.text }}>
                  {t('documents.basicInformation') || 'Basic Information'}
                </h4>
                <div className="info-grid">
                  {document.isbn && (
                    <div className="info-item">
                      <label style={{ color: colors.textSecondary }}>ISBN:</label>
                      <span style={{ color: colors.text }}>{document.isbn}</span>
                    </div>
                  )}
                  {document.subject && (
                    <div className="info-item">
                      <label style={{ color: colors.textSecondary }}>
                        {t('documents.subject') || 'Subject'}:
                      </label>
                      <span style={{ color: colors.text }}>{document.subject}</span>
                    </div>
                  )}
                  {document.language && (
                    <div className="info-item">
                      <label style={{ color: colors.textSecondary }}>
                        {t('documents.language') || 'Language'}:
                      </label>
                      <span style={{ color: colors.text }}>{document.language}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Type-specific information */}
              {renderTypeSpecificInfo()}

              {/* Publication Details */}
              <div
                className="detail-section transition-colors duration-200"
                style={{
                  backgroundColor: colors.surface,
                  borderColor: colors.border
                }}
              >
                <h4 style={{ color: colors.text }}>
                  {t('documents.publicationDetails') || 'Publication Details'}
                </h4>
                <div className="info-grid">
                  {document.publisher && (
                    <div className="info-item">
                      <label style={{ color: colors.textSecondary }}>
                        {t('documents.publisher') || 'Publisher'}:
                      </label>
                      <span style={{ color: colors.text }}>{document.publisher}</span>
                    </div>
                  )}
                  {document.publication_date && (
                    <div className="info-item">
                      <label style={{ color: colors.textSecondary }}>
                        {t('documents.publicationDate') || 'Publication Date'}:
                      </label>
                      <span style={{ color: colors.text }}>{formatDate(document.publication_date)}</span>
                    </div>
                  )}
                  {document.pages && (
                    <div className="info-item">
                      <label style={{ color: colors.textSecondary }}>
                        {t('documents.pages') || 'Pages'}:
                      </label>
                      <span style={{ color: colors.text }}>{document.pages}</span>
                    </div>
                  )}
                  {document.format && (
                    <div className="info-item">
                      <label style={{ color: colors.textSecondary }}>
                        {t('documents.format') || 'Format'}:
                      </label>
                      <span style={{ color: colors.text }}>{document.format}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Physical Location */}
              {document.location && (
                <div
                  className="detail-section transition-colors duration-200"
                  style={{
                    backgroundColor: colors.surface,
                    borderColor: colors.border
                  }}
                >
                  <h4 style={{ color: colors.text }}>
                    {t('documents.location') || 'Location'}
                  </h4>
                  <div className="info-item">
                    <label style={{ color: colors.textSecondary }}>
                      {t('documents.shelfLocation') || 'Shelf Location'}:
                    </label>
                    <span
                      className="location"
                      style={{ color: colors.text }}
                    >
                      {document.location}
                    </span>
                  </div>
                </div>
              )}

              {/* Keywords */}
              {document.keywords && (
                <div
                  className="detail-section transition-colors duration-200"
                  style={{
                    backgroundColor: colors.surface,
                    borderColor: colors.border
                  }}
                >
                  <h4 style={{ color: colors.text }}>
                    {t('documents.keywords') || 'Keywords'}
                  </h4>
                  <div className="keywords">
                    {document.keywords.split(',').map((keyword, index) => (
                      <span
                        key={index}
                        className="keyword-tag transition-colors duration-200"
                        style={{
                          backgroundColor: colors.primary + '20',
                          color: colors.primary,
                          borderColor: colors.primary
                        }}
                      >
                        {keyword.trim()}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Metadata */}
              <div
                className="detail-section transition-colors duration-200"
                style={{
                  backgroundColor: colors.surface,
                  borderColor: colors.border
                }}
              >
                <h4 style={{ color: colors.text }}>
                  {t('documents.metadata') || 'Metadata'}
                </h4>
                <div className="info-grid">
                  <div className="info-item">
                    <label style={{ color: colors.textSecondary }}>
                      {t('documents.added') || 'Added'}:
                    </label>
                    <span style={{ color: colors.text }}>{formatDate(document.created_at)}</span>
                  </div>
                  <div className="info-item">
                    <label style={{ color: colors.textSecondary }}>
                      {t('documents.lastUpdated') || 'Last Updated'}:
                    </label>
                    <span style={{ color: colors.text }}>{formatDate(document.updated_at)}</span>
                  </div>
                  {document.creator_name && (
                    <div className="info-item">
                      <label style={{ color: colors.textSecondary }}>
                        {t('documents.addedBy') || 'Added by'}:
                      </label>
                      <span style={{ color: colors.text }}>{document.creator_name}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          className="document-detail-actions transition-colors duration-200"
          style={{
            backgroundColor: colors.surface,
            borderTopColor: colors.border
          }}
        >
          <button
            onClick={onClose}
            className="btn-secondary transition-colors duration-200"
            style={{
              backgroundColor: colors.background,
              color: colors.text,
              borderColor: colors.border
            }}
          >
            {t('common.close') || 'Close'}
          </button>

          {isLibrarian && (
            <button
              onClick={() => onEdit(document)}
              className="btn-primary transition-colors duration-200"
              style={{
                backgroundColor: colors.primary,
                color: 'white',
                borderColor: colors.primary
              }}
            >
              {t('documents.editDocument') || 'Edit Document'}
            </button>
          )}

          {document.available_copies > 0 && (
            <button
              className="btn-success transition-colors duration-200"
              style={{
                backgroundColor: colors.success,
                color: 'white',
                borderColor: colors.success
              }}
            >
              {t('documents.reserveDocument') || 'Reserve Document'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentDetail;
