# Surveillance des Microservices - Guide d'utilisation

## Vue d'ensemble

Ce guide explique comment utiliser les composants de surveillance des microservices dans l'application BiblioAI pour vérifier que tous les services fonctionnent correctement depuis le frontend.

## Architecture des Microservices

L'application BiblioAI utilise les microservices suivants :

- **API Gateway** (Port 5000) - Point d'entrée principal
- **Auth Service** (Port 5001) - Authentification et autorisation
- **Document Service** (Port 5002) - Gestion des documents
- **Classification Service** (Port 5003) - Classification automatique
- **Search Service** (Port 5004) - Recherche et indexation

## Composants de Surveillance

### 1. Service Health Service (`healthService.js`)

Service principal pour vérifier l'état des microservices.

**Fonctions principales :**
- `checkAllServicesHealth()` - Vérifie tous les services
- `checkServiceHealth(serviceName)` - Vérifie un service spécifique
- `getSystemStats()` - Obtient les statistiques globales
- `testServiceEndpoints()` - Teste les endpoints principaux

**Exemple d'utilisation :**
```javascript
import { checkAllServicesHealth, getSystemStats } from '../services/healthService';

// Vérifier tous les services
const healthResults = await checkAllServicesHealth();
console.log(healthResults);

// Obtenir les statistiques
const stats = await getSystemStats();
console.log(`${stats.healthyServices}/${stats.totalServices} services actifs`);
```

### 2. Hook useServiceHealth (`useServiceHealth.js`)

Hook React personnalisé pour faciliter l'utilisation de la surveillance.

**Fonctionnalités :**
- Surveillance automatique en temps réel
- Gestion de l'état de chargement
- Fonctions utilitaires pour l'état des services

**Exemple d'utilisation :**
```javascript
import useServiceHealth from '../hooks/useServiceHealth';

function MyComponent() {
  const { 
    systemStats, 
    isLoading, 
    error, 
    isSystemHealthy,
    getHealthSummary,
    refresh 
  } = useServiceHealth({
    autoRefresh: true,
    refreshInterval: 30000
  });

  if (isLoading) return <div>Chargement...</div>;
  if (error) return <div>Erreur: {error}</div>;

  return (
    <div>
      <h2>État du système: {getHealthSummary()}</h2>
      <button onClick={refresh}>Actualiser</button>
    </div>
  );
}
```

### 3. Composant ServicesDashboard (`ServicesDashboard.js`)

Tableau de bord complet pour visualiser l'état de tous les microservices.

**Fonctionnalités :**
- Vue d'ensemble des statistiques système
- État détaillé de chaque service
- Tests des endpoints principaux
- Actualisation automatique et manuelle

**Utilisation :**
```javascript
import ServicesDashboard from '../components/ServicesDashboard';

// Dans votre route
<Route path="/services-dashboard" element={<ServicesDashboard />} />
```

### 4. Composant ServiceStatusIndicator (`ServiceStatusIndicator.js`)

Indicateur compact pour afficher l'état des services dans la navigation.

**Modes d'affichage :**
- Compact (pour la barre de navigation)
- Détaillé (pour les pages)

**Utilisation :**
```javascript
import ServiceStatusIndicator from '../components/ServiceStatusIndicator';

// Version compacte pour la navigation
<ServiceStatusIndicator />

// Version détaillée
<ServiceStatusIndicator showDetails={true} />
```

### 5. Composant ServiceHealthNotification (`ServiceHealthNotification.js`)

Système de notifications pour alerter l'utilisateur des problèmes.

**Types de notifications :**
- Erreurs critiques (persistantes)
- Avertissements (services hors ligne)
- Notifications de récupération
- Problèmes de connectivité réseau

**Utilisation :**
```javascript
import ServiceHealthNotification from '../components/ServiceHealthNotification';

// À placer dans App.js pour une couverture globale
function App() {
  return (
    <div>
      <ServiceHealthNotification />
      {/* Reste de l'application */}
    </div>
  );
}
```

## Intégration dans l'Application

### Étape 1: Ajouter les notifications globales

Dans `App.js` :
```javascript
import ServiceHealthNotification from './components/ServiceHealthNotification';

function App() {
  return (
    <Router>
      <div className="App">
        <ServiceHealthNotification />
        {/* Routes */}
      </div>
    </Router>
  );
}
```

### Étape 2: Ajouter l'indicateur dans la navigation

Dans votre composant de navigation :
```javascript
import ServiceStatusIndicator from '../components/ServiceStatusIndicator';

function Navigation() {
  return (
    <nav>
      {/* Autres éléments de navigation */}
      <ServiceStatusIndicator />
    </nav>
  );
}
```

### Étape 3: Ajouter la route du tableau de bord

Dans `App.js` :
```javascript
<Route 
  path="/services-dashboard" 
  element={
    <ProtectedRoute>
      <ServicesDashboard />
    </ProtectedRoute>
  } 
/>
```

## Surveillance en Temps Réel

### Configuration de l'actualisation automatique

```javascript
const { systemStats } = useServiceHealth({
  autoRefresh: true,        // Activer l'actualisation automatique
  refreshInterval: 30000,   // Intervalle en millisecondes (30 secondes)
  includeEndpointTests: true // Inclure les tests d'endpoints
});
```

### Gestion des erreurs

```javascript
const { error, networkStatus, hasNetworkConnection } = useServiceHealth();

if (!hasNetworkConnection) {
  return <div>Pas de connexion réseau</div>;
}

if (error) {
  return <div>Erreur: {error}</div>;
}
```

## Personnalisation

### Modifier les URLs des services

Dans `healthService.js`, modifiez l'objet `MICROSERVICES` :
```javascript
const MICROSERVICES = {
  'API Gateway': 'http://localhost:5000',
  'Auth Service': 'http://localhost:5001',
  // Ajoutez vos services personnalisés
  'Custom Service': 'http://localhost:5005'
};
```

### Ajouter des tests d'endpoints personnalisés

Dans `testServiceEndpoints()`, ajoutez vos tests :
```javascript
const tests = {
  'Custom Service': [
    {
      name: 'Custom Endpoint',
      endpoint: '/custom',
      method: 'GET',
      requiresAuth: true
    }
  ]
};
```

## Bonnes Pratiques

1. **Surveillance continue** : Utilisez l'actualisation automatique pour une surveillance en temps réel
2. **Gestion des erreurs** : Toujours gérer les cas d'erreur et de perte de connectivité
3. **Performance** : Ajustez les intervalles d'actualisation selon vos besoins
4. **UX** : Utilisez les notifications pour informer l'utilisateur sans être intrusif
5. **Sécurité** : Les endpoints de santé ne doivent pas exposer d'informations sensibles

## Dépannage

### Services non détectés
- Vérifiez que les services sont démarrés
- Vérifiez les URLs dans `MICROSERVICES`
- Vérifiez les endpoints `/health` de chaque service

### Erreurs CORS
- Configurez CORS sur vos microservices
- Vérifiez les headers d'authentification

### Performance lente
- Augmentez les timeouts dans `healthService.js`
- Réduisez la fréquence d'actualisation
- Optimisez les endpoints de santé des services
