import React, { useState } from 'react';
import { login, register } from '../services/authService';
import { getDocuments, generateBarcode, createDocument } from '../services/documentService';

const MicroservicesTester = () => {
  const [testResults, setTestResults] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [testData, setTestData] = useState({
    username: 'test_user_' + Date.now(),
    email: '<EMAIL>',
    password: 'test123'
  });

  const updateResult = (testName, result) => {
    setTestResults(prev => ({
      ...prev,
      [testName]: result
    }));
  };

  const testAuthService = async () => {
    try {
      updateResult('auth_register', { status: 'testing', message: 'Testing registration...' });
      
      // Test d'inscription
      const registerResult = await register({
        username: testData.username,
        email: testData.email,
        password: testData.password,
        role: 'member'
      });
      
      updateResult('auth_register', { 
        status: 'success', 
        message: 'Registration successful',
        data: registerResult 
      });

      // Test de connexion
      updateResult('auth_login', { status: 'testing', message: 'Testing login...' });
      
      const loginResult = await login({
        username: testData.username,
        password: testData.password
      });
      
      updateResult('auth_login', { 
        status: 'success', 
        message: 'Login successful',
        data: { token: loginResult.access_token ? 'Token received' : 'No token' }
      });

    } catch (error) {
      updateResult('auth_register', { 
        status: 'error', 
        message: error.message || 'Auth service error' 
      });
    }
  };

  const testDocumentService = async () => {
    try {
      // Test génération de code-barres
      updateResult('doc_barcode', { status: 'testing', message: 'Testing barcode generation...' });
      
      const barcodeResult = await generateBarcode();
      updateResult('doc_barcode', { 
        status: 'success', 
        message: 'Barcode generated successfully',
        data: { barcode: barcodeResult.barcode }
      });

      // Test récupération des documents
      updateResult('doc_list', { status: 'testing', message: 'Testing document retrieval...' });
      
      const documentsResult = await getDocuments();
      updateResult('doc_list', { 
        status: 'success', 
        message: `Retrieved ${documentsResult.documents?.length || 0} documents`,
        data: { count: documentsResult.documents?.length || 0 }
      });

      // Test création de document
      updateResult('doc_create', { status: 'testing', message: 'Testing document creation...' });
      
      const newDocument = {
        title: 'Test Document - ' + new Date().toISOString(),
        description: 'Document de test créé automatiquement',
        document_type: 'book',
        barcode: barcodeResult.barcode,
        author: 'Test Author',
        category: 'Technology',
        subject: 'Testing',
        keywords: 'test, microservices',
        publisher: 'Test Publisher',
        language: 'French',
        pages: 100,
        format: 'paperback',
        total_copies: 1,
        available_copies: 1
      };

      const createResult = await createDocument(newDocument);
      updateResult('doc_create', { 
        status: 'success', 
        message: 'Document created successfully',
        data: { id: createResult.document?.id }
      });

    } catch (error) {
      updateResult('doc_barcode', { 
        status: 'error', 
        message: error.message || 'Document service error' 
      });
    }
  };

  const testClassificationService = async () => {
    try {
      updateResult('classification', { status: 'testing', message: 'Testing classification...' });
      
      // Test de classification via l'API Gateway
      const response = await fetch('http://localhost:5000/api/classification/classify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          title: 'Introduction to Machine Learning',
          description: 'A comprehensive guide to machine learning algorithms and applications'
        })
      });

      if (response.ok) {
        const result = await response.json();
        updateResult('classification', { 
          status: 'success', 
          message: 'Classification successful',
          data: result
        });
      } else {
        throw new Error(`HTTP ${response.status}`);
      }

    } catch (error) {
      updateResult('classification', { 
        status: 'error', 
        message: error.message || 'Classification service error' 
      });
    }
  };

  const testSearchService = async () => {
    try {
      updateResult('search', { status: 'testing', message: 'Testing search...' });
      
      // Test de recherche
      const response = await fetch('http://localhost:5000/api/search?q=test', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        updateResult('search', { 
          status: 'success', 
          message: `Search completed - ${result.results?.length || 0} results`,
          data: { count: result.results?.length || 0 }
        });
      } else {
        throw new Error(`HTTP ${response.status}`);
      }

      // Test des statistiques
      updateResult('search_stats', { status: 'testing', message: 'Testing search stats...' });
      
      const statsResponse = await fetch('http://localhost:5000/api/search/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (statsResponse.ok) {
        const statsResult = await statsResponse.json();
        updateResult('search_stats', { 
          status: 'success', 
          message: 'Stats retrieved successfully',
          data: statsResult
        });
      } else {
        throw new Error(`HTTP ${statsResponse.status}`);
      }

    } catch (error) {
      updateResult('search', { 
        status: 'error', 
        message: error.message || 'Search service error' 
      });
    }
  };

  const runAllTests = async () => {
    setIsLoading(true);
    setTestResults({});
    
    try {
      await testAuthService();
      await testDocumentService();
      await testClassificationService();
      await testSearchService();
    } catch (error) {
      console.error('Error running tests:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'testing': return '⏳';
      default: return '⚪';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50';
      case 'error': return 'text-red-600 bg-red-50';
      case 'testing': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">
          🧪 Test des Microservices BiblioAI
        </h2>
        
        <div className="mb-6">
          <p className="text-gray-600 mb-4">
            Cliquez sur "Lancer tous les tests" pour vérifier que tous les microservices fonctionnent correctement.
          </p>
          
          <button
            onClick={runAllTests}
            disabled={isLoading}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? '⏳ Tests en cours...' : '🚀 Lancer tous les tests'}
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Auth Service Tests */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-700 mb-3">
              🔐 Service d'Authentification
            </h3>
            
            <div className="space-y-2">
              <div className={`p-3 rounded ${getStatusColor(testResults.auth_register?.status)}`}>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Inscription</span>
                  <span>{getStatusIcon(testResults.auth_register?.status)}</span>
                </div>
                {testResults.auth_register && (
                  <p className="text-sm mt-1">{testResults.auth_register.message}</p>
                )}
              </div>
              
              <div className={`p-3 rounded ${getStatusColor(testResults.auth_login?.status)}`}>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Connexion</span>
                  <span>{getStatusIcon(testResults.auth_login?.status)}</span>
                </div>
                {testResults.auth_login && (
                  <p className="text-sm mt-1">{testResults.auth_login.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Document Service Tests */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-700 mb-3">
              📚 Service de Documents
            </h3>
            
            <div className="space-y-2">
              <div className={`p-3 rounded ${getStatusColor(testResults.doc_barcode?.status)}`}>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Génération code-barres</span>
                  <span>{getStatusIcon(testResults.doc_barcode?.status)}</span>
                </div>
                {testResults.doc_barcode && (
                  <p className="text-sm mt-1">{testResults.doc_barcode.message}</p>
                )}
              </div>
              
              <div className={`p-3 rounded ${getStatusColor(testResults.doc_list?.status)}`}>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Liste documents</span>
                  <span>{getStatusIcon(testResults.doc_list?.status)}</span>
                </div>
                {testResults.doc_list && (
                  <p className="text-sm mt-1">{testResults.doc_list.message}</p>
                )}
              </div>
              
              <div className={`p-3 rounded ${getStatusColor(testResults.doc_create?.status)}`}>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Création document</span>
                  <span>{getStatusIcon(testResults.doc_create?.status)}</span>
                </div>
                {testResults.doc_create && (
                  <p className="text-sm mt-1">{testResults.doc_create.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Classification Service Tests */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-700 mb-3">
              🏷️ Service de Classification
            </h3>
            
            <div className={`p-3 rounded ${getStatusColor(testResults.classification?.status)}`}>
              <div className="flex items-center justify-between">
                <span className="font-medium">Classification automatique</span>
                <span>{getStatusIcon(testResults.classification?.status)}</span>
              </div>
              {testResults.classification && (
                <p className="text-sm mt-1">{testResults.classification.message}</p>
              )}
            </div>
          </div>

          {/* Search Service Tests */}
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-700 mb-3">
              🔍 Service de Recherche
            </h3>
            
            <div className="space-y-2">
              <div className={`p-3 rounded ${getStatusColor(testResults.search?.status)}`}>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Recherche</span>
                  <span>{getStatusIcon(testResults.search?.status)}</span>
                </div>
                {testResults.search && (
                  <p className="text-sm mt-1">{testResults.search.message}</p>
                )}
              </div>
              
              <div className={`p-3 rounded ${getStatusColor(testResults.search_stats?.status)}`}>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Statistiques</span>
                  <span>{getStatusIcon(testResults.search_stats?.status)}</span>
                </div>
                {testResults.search_stats && (
                  <p className="text-sm mt-1">{testResults.search_stats.message}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Résumé des résultats */}
        {Object.keys(testResults).length > 0 && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-semibold text-gray-700 mb-2">Résumé des tests</h4>
            <div className="flex space-x-4 text-sm">
              <span className="text-green-600">
                ✅ Réussis: {Object.values(testResults).filter(r => r.status === 'success').length}
              </span>
              <span className="text-red-600">
                ❌ Échoués: {Object.values(testResults).filter(r => r.status === 'error').length}
              </span>
              <span className="text-blue-600">
                ⏳ En cours: {Object.values(testResults).filter(r => r.status === 'testing').length}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MicroservicesTester;
