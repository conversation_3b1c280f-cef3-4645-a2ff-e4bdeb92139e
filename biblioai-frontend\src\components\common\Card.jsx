import React from 'react';
import PropTypes from 'prop-types';
import { useTheme } from '../../contexts/ThemeContext';

const Card = ({
  children,
  className = '',
  hasShadow = true,
  hasBorder = false,
  ...props
}) => {
  const { getCurrentColors } = useTheme();
  const colors = getCurrentColors();

  return (
    <div
      className={`
        rounded-lg transition-colors duration-200
        ${hasShadow ? 'shadow-md' : ''}
        ${hasBorder ? 'border' : ''}
        ${className}
      `}
      style={{
        backgroundColor: colors.surface,
        borderColor: hasBorder ? colors.border : 'transparent'
      }}
      {...props}
    >
      {children}
    </div>
  );
};

Card.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  hasShadow: PropTypes.bool,
  hasBorder: PropTypes.bool,
};

export default Card;