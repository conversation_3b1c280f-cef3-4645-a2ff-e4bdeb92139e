/* Member Services Styles */

/* Dashboard Layout */
.member-dashboard {
  min-height: 100vh;
  background-color: #f9fafb;
}

.dashboard-header {
  background: linear-gradient(135deg, #4475f2 0%, #3461d1 100%);
  color: white;
  padding: 2rem 0;
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* Tab Navigation */
.tab-navigation {
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 2rem;
}

.tab-button {
  padding: 0.75rem 1rem;
  border-bottom: 2px solid transparent;
  font-weight: 500;
  font-size: 0.875rem;
  color: #6b7280;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #374151;
  border-bottom-color: #d1d5db;
}

.tab-button.active {
  color: #2563eb;
  border-bottom-color: #2563eb;
}

/* Summary Cards */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summary-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.summary-card-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

.summary-card-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.summary-card-value {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
}

/* List Items */
.list-container {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.list-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: between;
  align-items: center;
}

.list-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.list-item {
  padding: 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.list-item:hover {
  background-color: #f9fafb;
}

.list-item:last-child {
  border-bottom: none;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-active {
  background-color: #d1fae5;
  color: #065f46;
}

.status-returned {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-overdue {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-cancelled {
  background-color: #f3f4f6;
  color: #374151;
}

.status-fulfilled {
  background-color: #e0e7ff;
  color: #5b21b6;
}

.status-expired {
  background-color: #fed7aa;
  color: #9a3412;
}

/* Priority Badges */
.priority-urgent {
  background-color: #fee2e2;
  color: #991b1b;
}

.priority-high {
  background-color: #fed7aa;
  color: #9a3412;
}

.priority-normal {
  background-color: #dbeafe;
  color: #1e40af;
}

.priority-low {
  background-color: #f3f4f6;
  color: #374151;
}

/* Notification Panel */
.notification-panel {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  width: 24rem;
  background: white;
  box-shadow: -4px 0 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 50;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.notification-panel.open {
  transform: translateX(0);
}

.notification-panel-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-panel-content {
  flex: 1;
  overflow-y: auto;
}

.notification-item {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  border-left: 4px solid transparent;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: #f9fafb;
}

.notification-item.unread {
  background-color: #eff6ff;
  border-left-color: #2563eb;
}

.notification-item.priority-urgent {
  border-left-color: #dc2626;
}

.notification-item.priority-high {
  border-left-color: #ea580c;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: white;
  color: #374151;
  border-color: #d1d5db;
}

.btn-secondary:hover {
  background-color: #f9fafb;
}

.btn-danger {
  background-color: #dc2626;
  color: white;
}

.btn-danger:hover {
  background-color: #b91c1c;
}

.btn-success {
  background-color: #059669;
  color: white;
}

.btn-success:hover {
  background-color: #047857;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

/* Loading States */
.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid #f3f4f6;
  border-radius: 50%;
  border-top-color: #2563eb;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-state-icon {
  width: 3rem;
  height: 3rem;
  margin: 0 auto 1rem;
  color: #9ca3af;
}

.empty-state-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
}

.empty-state-description {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-content {
    padding: 1rem;
  }
  
  .summary-cards {
    grid-template-columns: 1fr;
  }
  
  .notification-panel {
    width: 100%;
  }
  
  .list-item {
    padding: 1rem;
  }
  
  .tab-navigation {
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .tab-button {
    display: inline-block;
    min-width: max-content;
  }
}

/* Accessibility */
.btn:focus,
.tab-button:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

.notification-item:focus {
  outline: 2px solid #2563eb;
  outline-offset: -2px;
}
