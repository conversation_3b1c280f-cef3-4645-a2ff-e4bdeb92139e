/* LibrarianDashboard.css - Creative Design */

:root {
  --primary: #6C5CE7;
  --primary-light: #A29BFE;
  --secondary: #00B894;
  --accent: #FD79A8;
  --dark: #2D3436;
  --light: #F5F6FA;
  --text: #2D3436;
  --text-light: #636E72;
  --card-bg: #FFFFFF;
  --sidebar-bg: linear-gradient(135deg, #2D3436 0%, #000000 100%);
  --success: #00B894;
  --warning: #FDCB6E;
  --danger: #E17055;
}

/* Base Styles */
.librarian-dashboard {
  font-family: 'Poppins', 'Segoe UI', sans-serif;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--light);
  color: var(--text);
  overflow: hidden;
}

.dashboard-container {
  display: flex;
  flex: 1;
  overflow-x: hidden;
  min-height: 0;
}

/* Creative Sidebar */
.dashboard-sidebar {
  width: 300px;
  background: var(--sidebar-bg);
  color: white;
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;
  box-shadow: 4px 0 15px rgba(0,0,0,0.1);
}

.dashboard-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(108,92,231,0.15) 0%, rgba(0,0,0,0) 50%);
  pointer-events: none;
}

.user-info {
  padding-bottom: 1.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  position: relative;
}

.user-info h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: white;
}

.user-role {
  font-size: 0.85rem;
  color: var(--primary-light);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.dashboard-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex-grow: 1;
}

.nav-item {
  background: none;
  border: none;
  color: rgba(255,255,255,0.8);
  text-align: left;
  padding: 1rem 1.25rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: 0.5s;
}

.nav-item:hover {
  color: white;
  background: rgba(255,255,255,0.05);
  transform: translateX(5px);
}

.nav-item:hover::before {
  left: 100%;
}

.nav-item.active {
  background: var(--primary);
  color: white;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(108,92,231,0.3);
}

.nav-item svg {
  width: 1.25rem;
  height: 1.25rem;
}

.sidebar-footer {
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid rgba(255,255,255,0.1);
}

.logout-btn {
  background: rgba(255,255,255,0.1);
  border: none;
  color: white;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: rgba(255,255,255,0.2);
  transform: translateY(-2px);
}

/* Main Content Area */
.dashboard-content {
  flex: 1;
  padding: 2.5rem;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: var(--light);
  position: relative;
  min-height: 0;
  box-sizing: border-box;
}

.dashboard-content::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 200%;
  background: radial-gradient(circle, rgba(108,92,231,0.05) 0%, rgba(108,92,231,0) 70%);
  pointer-events: none;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  position: relative;
}

.section-header h2 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  color: var(--dark);
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 50px;
  height: 4px;
  background: var(--primary);
  border-radius: 2px;
}

/* Creative Buttons */
.btn-primary {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.85rem 1.75rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.95rem;
  box-shadow: 0 4px 15px rgba(108,92,231,0.3);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary:hover {
  background: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108,92,231,0.4);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background: white;
  color: var(--primary);
  border: 2px solid var(--primary-light);
  padding: 0.85rem 1.75rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-secondary:hover {
  background: var(--primary-light);
  color: white;
  border-color: var(--primary-light);
  transform: translateY(-2px);
}

.add-document-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Creative Card Styles */
.analytics-section, .classification-section {
  padding: 1rem 0;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 1.75rem;
  margin-top: 1.5rem;
}

.analytics-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 1.75rem;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0,0,0,0.05);
  position: relative;
  overflow: hidden;
}

.analytics-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.analytics-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--primary);
}

.analytics-card:nth-child(2)::before {
  background: var(--secondary);
}

.analytics-card:nth-child(3)::before {
  background: var(--accent);
}

.analytics-card:nth-child(4)::before {
  background: var(--warning);
}

.analytics-card h3 {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0 0 1rem 0;
  color: var(--text-light);
}

.analytics-number {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text);
  background: linear-gradient(to right, var(--primary), var(--primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.analytics-card:nth-child(2) .analytics-number {
  background: linear-gradient(to right, var(--secondary), #55EFC4);
  -webkit-background-clip: text;
}

.analytics-card:nth-child(3) .analytics-number {
  background: linear-gradient(to right, var(--accent), #FF7675);
  -webkit-background-clip: text;
}

.analytics-card:nth-child(4) .analytics-number {
  background: linear-gradient(to right, var(--warning), #FAB1A0);
  -webkit-background-clip: text;
}

.analytics-text {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text);
}

.analytics-card p {
  font-size: 0.9rem;
  color: var(--text-light);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Classification Tools */
.classification-tools {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.75rem;
  margin-top: 1.5rem;
}

.tool-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
  border: 1px solid rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tool-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.tool-card h3 {
  font-size: 1.4rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text);
  position: relative;
  display: inline-block;
}

.tool-card h3::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 3px;
  background: var(--primary);
  border-radius: 2px;
}

.tool-card:nth-child(2) h3::after {
  background: var(--secondary);
}

.tool-card:nth-child(3) h3::after {
  background: var(--accent);
}

.tool-card p {
  font-size: 1rem;
  color: var(--text-light);
  margin: 0 0 2rem 0;
  flex-grow: 1;
  line-height: 1.6;
}

/* Form Styles */
.classification-form {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  max-width: 700px;
  margin: 2rem auto 0;
  border: 1px solid rgba(0,0,0,0.05);
}

.classification-form h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: var(--text);
}

.classification-form p {
  font-size: 1rem;
  color: var(--text-light);
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.95rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
  color: var(--text);
}

.form-control {
  width: 100%;
  padding: 1rem;
  border: 1px solid #dfe6e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: white;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-light);
  box-shadow: 0 0 0 3px rgba(108,92,231,0.1);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

/* Result Styles */
.classification-result {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  max-width: 700px;
  margin: 2rem auto 0;
  border: 1px solid rgba(0,0,0,0.05);
}

.result-card {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  border-left: 4px solid var(--primary);
}

.result-item {
  display: flex;
  margin-bottom: 1.25rem;
  align-items: flex-start;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-label {
  font-weight: 600;
  min-width: 120px;
  color: var(--text-light);
}

.result-value {
  font-weight: 500;
  color: var(--text);
  flex-grow: 1;
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.keyword-tag {
  background-color: var(--primary-light);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.result-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .dashboard-sidebar {
    width: 260px;
  }
}

@media (max-width: 992px) {
  .dashboard-sidebar {
    width: 240px;
    padding: 1.5rem 1rem;
  }
  
  .dashboard-content {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: column;
  }
  
  .dashboard-sidebar {
    width: 100%;
    padding: 1rem;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .user-info {
    width: 100%;
    margin-bottom: 1rem;
  }
  
  .dashboard-nav {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .nav-item {
    padding: 0.75rem 1rem;
  }
  
  .sidebar-footer {
    margin-top: 0;
    border-top: none;
    margin-left: auto;
  }
  
  .dashboard-content {
    padding: 1.5rem;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }
}

@media (max-width: 576px) {
  .analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .classification-tools {
    grid-template-columns: 1fr;
  }
  
  .form-actions,
  .result-actions {
    flex-direction: column;
  }
  
  .form-actions button,
  .result-actions button {
    width: 100%;
  }
  
  .section-header h2 {
    font-size: 1.75rem;
  }
  
  .classification-form,
  .classification-result {
    padding: 1.5rem;
  }
}