import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Statistics Service
export const statisticsService = {
  // Get comprehensive library statistics
  getLibraryStatistics: async () => {
    try {
      console.log('STATS SERVICE: Fetching library statistics...');
      const response = await api.get('/api/documents/statistics');
      console.log('STATS SERVICE: Statistics received:', response.data);
      return response.data;
    } catch (error) {
      console.error('STATS SERVICE: Error fetching statistics:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch statistics');
    }
  },

  // Get search service statistics
  getSearchStatistics: async () => {
    try {
      console.log('STATS SERVICE: Fetching search statistics...');
      const response = await api.get('/api/search/stats');
      console.log('STATS SERVICE: Search statistics received:', response.data);
      return response.data;
    } catch (error) {
      console.error('STATS SERVICE: Error fetching search statistics:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch search statistics');
    }
  },

  // Get combined statistics from all services
  getAllStatistics: async () => {
    try {
      console.log('STATS SERVICE: Fetching all statistics...');
      
      const [libraryStats, searchStats] = await Promise.allSettled([
        statisticsService.getLibraryStatistics(),
        statisticsService.getSearchStatistics()
      ]);

      const result = {
        library: libraryStats.status === 'fulfilled' ? libraryStats.value : null,
        search: searchStats.status === 'fulfilled' ? searchStats.value : null,
        errors: []
      };

      if (libraryStats.status === 'rejected') {
        result.errors.push(`Library stats: ${libraryStats.reason.message}`);
      }

      if (searchStats.status === 'rejected') {
        result.errors.push(`Search stats: ${searchStats.reason.message}`);
      }

      console.log('STATS SERVICE: All statistics compiled:', result);
      return result;
    } catch (error) {
      console.error('STATS SERVICE: Error fetching all statistics:', error);
      throw new Error('Failed to fetch comprehensive statistics');
    }
  },

  // Calculate derived statistics
  calculateDerivedStats: (libraryStats) => {
    if (!libraryStats || !libraryStats.overview) {
      return {};
    }

    const overview = libraryStats.overview;
    
    return {
      // Utilization rate
      utilizationRate: overview.total_copies > 0 
        ? Math.round(((overview.total_copies - overview.available_copies) / overview.total_copies) * 100)
        : 0,
      
      // Collection diversity (number of different categories)
      categoryDiversity: Object.keys(libraryStats.by_category || {}).length,
      
      // Average copies per document
      avgCopiesPerDocument: overview.total_documents > 0 
        ? Math.round((overview.total_copies / overview.total_documents) * 10) / 10
        : 0,
      
      // Image coverage percentage
      imageCoveragePercentage: overview.total_documents > 0
        ? Math.round((overview.documents_with_images / overview.total_documents) * 100)
        : 0,
      
      // Most common document type
      mostCommonType: libraryStats.by_type 
        ? Object.entries(libraryStats.by_type).reduce((a, b) => a[1] > b[1] ? a : b, ['', 0])
        : ['', 0],
      
      // Collection growth rate (based on recent additions)
      growthRate: overview.total_documents > 0
        ? Math.round((overview.recent_additions / overview.total_documents) * 100 * 10) / 10
        : 0
    };
  },

  // Format statistics for display
  formatStatistics: (stats) => {
    if (!stats || !stats.library) {
      return null;
    }

    const library = stats.library;
    const derived = statisticsService.calculateDerivedStats(library);

    return {
      // Main overview cards
      overview: [
        {
          title: 'Total Documents',
          value: library.overview.total_documents.toLocaleString(),
          subtitle: `+${library.overview.recent_additions} this month`,
          icon: '📚',
          color: 'primary'
        },
        {
          title: 'Available Documents',
          value: library.overview.available_documents.toLocaleString(),
          subtitle: `${library.overview.availability_percentage}% availability`,
          icon: '✅',
          color: 'success'
        },
        {
          title: 'Total Copies',
          value: library.overview.total_copies.toLocaleString(),
          subtitle: `${library.overview.available_copies} available`,
          icon: '📖',
          color: 'info'
        },
        {
          title: 'Collection Utilization',
          value: `${derived.utilizationRate}%`,
          subtitle: 'Currently in use',
          icon: '📊',
          color: 'warning'
        }
      ],

      // Secondary metrics
      metrics: [
        {
          title: 'Category Diversity',
          value: derived.categoryDiversity,
          subtitle: 'Different categories'
        },
        {
          title: 'Avg Copies/Document',
          value: derived.avgCopiesPerDocument,
          subtitle: 'Copies per title'
        },
        {
          title: 'Image Coverage',
          value: `${derived.imageCoveragePercentage}%`,
          subtitle: 'Documents with images'
        },
        {
          title: 'Growth Rate',
          value: `${derived.growthRate}%`,
          subtitle: 'Monthly growth'
        }
      ],

      // Charts data
      charts: {
        documentTypes: Object.entries(library.by_type || {}).map(([type, count]) => ({
          name: type.charAt(0).toUpperCase() + type.slice(1),
          value: count
        })),
        
        categories: Object.entries(library.by_category || {})
          .sort((a, b) => b[1] - a[1])
          .slice(0, 10)
          .map(([category, count]) => ({
            name: category,
            value: count
          })),
        
        languages: Object.entries(library.by_language || {}).map(([language, count]) => ({
          name: language,
          value: count
        })),
        
        topPublishers: library.top_publishers || []
      },

      // Popular items
      popular: {
        category: library.most_popular_category,
        type: derived.mostCommonType[0] || 'N/A'
      },

      // Search statistics (if available)
      search: stats.search || null
    };
  }
};

export default statisticsService;
