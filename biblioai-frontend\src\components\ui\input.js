import React from 'react';

export const Input = ({ 
  className = '', 
  type = 'text',
  disabled = false,
  ...props 
}) => {
  const baseClasses = 'flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50';
  
  return (
    <input
      type={type}
      className={`${baseClasses} ${className}`}
      disabled={disabled}
      {...props}
    />
  );
};
