import React, { useState } from 'react';
import { createReservation } from '../../services/memberService';

const ReserveButton = ({ document, onReservationSuccess, className = '' }) => {
  const [isReserving, setIsReserving] = useState(false);

  const handleReserve = async () => {
    if (!window.confirm(`Are you sure you want to reserve "${document.title}"?`)) {
      return;
    }

    try {
      setIsReserving(true);
      await createReservation({ document_id: document.id });
      
      if (onReservationSuccess) {
        onReservationSuccess(document);
      }
      
      alert(`Successfully reserved "${document.title}"!`);
    } catch (err) {
      alert(err.message || 'Failed to reserve book');
    } finally {
      setIsReserving(false);
    }
  };

  const isAvailable = document.available_copies > 0;

  if (!isAvailable) {
    return (
      <button
        disabled
        className={`inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed ${className}`}
      >
        Unavailable
      </button>
    );
  }

  return (
    <button
      onClick={handleReserve}
      disabled={isReserving}
      className={`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 ${className}`}
    >
      {isReserving ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          Reserving...
        </>
      ) : (
        <>
          <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Reserve
        </>
      )}
    </button>
  );
};

export default ReserveButton;
