# BiblioAI - Sprint 1: Project Setup and Basic Authentication

## Sprint Goals
- Set up project structure (Flask backend and React frontend)
- Implement basic user authentication (login/register)
- Create database models for users with different roles (librarian, member, clerk, manager)
- Develop basic UI components

## Backend Implementation Details

### Project Structure
```
biblioai-backend/
├── app/
│   ├── models/
│   │   └── user.py
│   ├── routes/
│   │   └── auth.py
│   ├── static/
│   ├── templates/
│   └── __init__.py
├── requirements.txt
└── run.py
```

### Features Implemented
1. **User Model**
   - Supports different user roles (member, librarian, clerk, manager)
   - Secure password hashing
   - User authentication

2. **Authentication API Endpoints**
   - User registration
   - User login with JWT token generation
   - User profile retrieval

### Technologies Used
- Flask: Web framework
- SQLAlchemy: ORM for database operations
- JWT: For secure authentication
- MySQL: Database management system

## Frontend Implementation Details

### Project Structure
```
biblioai-frontend/
├── public/
├── src/
│   ├── components/
│   │   ├── auth/
│   │   │   ├── Login.js
│   │   │   └── Register.js
│   │   ├── layout/
│   │   │   ├── Header.js
│   │   │   └── Footer.js
│   │   └── common/
│   ├── pages/
│   │   ├── HomePage.js
│   │   ├── LoginPage.js
│   │   └── RegisterPage.js
│   ├── services/
│   │   └── authService.js
│   ├── App.js
│   └── index.js
└── package.json
```

### Features Implemented
1. **Authentication Pages**
   - Login form
   - Registration form
   - Protected routes

2. **Basic Layout**
   - Header with navigation
   - Footer
   - Responsive design

### Technologies Used
- React: Frontend library
- React Router: For navigation
- Axios: For API requests
- Bootstrap or Material-UI: For styling

## Testing Instructions
1. Set up the MySQL database:
   ```
   cd biblioai-backend
   pip install -r requirements.txt
   python init_db.py
   ```

2. Configure your MySQL connection in the `.env` file:
   ```
   DB_USER=your_mysql_username
   DB_PASSWORD=your_mysql_password
   DB_HOST=localhost
   DB_NAME=biblioai
   ```

3. Start the backend server:
   ```
   python run.py
   ```

2. Start the frontend development server:
   ```
   cd biblioai-frontend
   npm install
   npm start
   ```

3. Access the application at http://localhost:3000

## Next Steps for Sprint 2
- Implement document models (books, periodicals, etc.)
- Create document classification system
- Develop document search functionality
- Build librarian interface for document management
