import axios from 'axios';

const API_URL = 'http://localhost:5000/api/members';

// Get auth token
const getAuthToken = () => {
  return localStorage.getItem('token');
};

// Get auth headers
const getAuthHeaders = () => {
  const token = getAuthToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// Reservation functions
export const getUserReservations = async () => {
  try {
    const response = await axios.get(`${API_URL}/reservations`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

export const createReservation = async (reservationData) => {
  try {
    const response = await axios.post(`${API_URL}/reservations`, reservationData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

export const cancelReservation = async (reservationId) => {
  try {
    const response = await axios.delete(`${API_URL}/reservations/${reservationId}`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Borrowing functions
export const getUserBorrowings = async () => {
  try {
    const response = await axios.get(`${API_URL}/borrowings`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

export const createBorrowing = async (borrowingData) => {
  try {
    const response = await axios.post(`${API_URL}/borrowings`, borrowingData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

export const returnDocument = async (borrowingId) => {
  try {
    const response = await axios.put(`${API_URL}/borrowings/${borrowingId}/return`, {}, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

export const renewBorrowing = async (borrowingId) => {
  try {
    const response = await axios.put(`${API_URL}/borrowings/${borrowingId}/renew`, {}, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// History functions
export const getUserHistory = async (page = 1, perPage = 10, action = '') => {
  try {
    const params = { page, per_page: perPage };
    if (action) params.action = action;
    
    const response = await axios.get(`${API_URL}/history`, {
      headers: getAuthHeaders(),
      params
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Dashboard functions
export const getDashboardSummary = async () => {
  try {
    const response = await axios.get(`${API_URL}/dashboard`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Admin functions (for librarians/managers)
export const getAllBorrowings = async (page = 1, perPage = 10, status = '') => {
  try {
    const params = { page, per_page: perPage };
    if (status) params.status = status;
    
    const response = await axios.get(`${API_URL}/admin/borrowings`, {
      headers: getAuthHeaders(),
      params
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

export const getOverdueBorrowings = async () => {
  try {
    const response = await axios.get(`${API_URL}/admin/overdue`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Utility functions
export const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

export const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export const getDaysUntilDue = (dueDateString) => {
  if (!dueDateString) return null;
  const dueDate = new Date(dueDateString);
  const today = new Date();
  const diffTime = dueDate - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

export const isOverdue = (dueDateString) => {
  const daysUntilDue = getDaysUntilDue(dueDateString);
  return daysUntilDue !== null && daysUntilDue < 0;
};

export const getStatusColor = (status) => {
  switch (status) {
    case 'active':
      return 'text-green-600 bg-green-100';
    case 'returned':
      return 'text-blue-600 bg-blue-100';
    case 'overdue':
      return 'text-red-600 bg-red-100';
    case 'cancelled':
      return 'text-gray-600 bg-gray-100';
    case 'fulfilled':
      return 'text-purple-600 bg-purple-100';
    case 'expired':
      return 'text-orange-600 bg-orange-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

export const getPriorityColor = (priority) => {
  switch (priority) {
    case 'urgent':
      return 'text-red-600 bg-red-100';
    case 'high':
      return 'text-orange-600 bg-orange-100';
    case 'normal':
      return 'text-blue-600 bg-blue-100';
    case 'low':
      return 'text-gray-600 bg-gray-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};
