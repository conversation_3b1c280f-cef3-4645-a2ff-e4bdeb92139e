/* Documents.css - Refined Design */

* {
  box-sizing: border-box;
}

body {
  overflow-x: hidden;
}

:root {
  --primary: #6C5CE7;
  --primary-light: #A29BFE;
  --secondary: #00B894;
  --accent: #FD79A8;
  --dark: #2D3436;
  --light: #F5F6FA;
  --card-bg: #FFFFFF;
  --success: #00B894;
  --warning: #FDCB6E;
  --danger: #E17055;
  --text: #2D3436;
  --text-light: #636E72;
}

/* Base Styles */
.document-list {
  font-family: 'Poppins', sans-serif;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  color: var(--text);
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Theme override styles - inline styles take priority */
.document-list[style*="background-color"] {
  background-color: inherit !important;
}

.document-card[style*="background-color"] {
  background-color: inherit !important;
}

.document-header[style*="background-color"] {
  background-color: inherit !important;
}

/* Header Section */
.document-list-header {
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.section-header h2 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  color: var(--dark);
  position: relative;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--primary);
  border-radius: 2px;
}

.add-document-btn {
  background-color: var(--primary);
  color: white;
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.add-document-btn:hover {
  background-color: var(--primary-light);
  transform: translateY(-2px);
}

/* Search and Filters */
.search-form {
  width: 100%;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

.search-input,
.filter-input,
.filter-select {
  padding: 0.75rem 1rem;
  border: 2px solid #dfe6e9;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 200px;
}

.search-btn {
  background-color: var(--primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

/* Documents Grid */
.documents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.document-card {
  background: var(--card-bg);
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
}

.document-card:hover {
  transform: translateY(-5px);
}

/* Document Images */
.document-card .document-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
    max-width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-bottom: 1px solid #eee;
}

.document-card .document-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Document Content */
.document-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.document-type-icon {
  font-size: 1.5rem;
  margin-right: 0.75rem;
}

.document-type {
  font-size: 0.85rem;
  text-transform: capitalize;
  color: var(--text-light);
  flex-grow: 1;
}

.availability {
  font-size: 0.75rem;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  font-weight: 500;
}

.available {
  background-color: rgba(0, 184, 148, 0.1);
  color: var(--success);
}

.unavailable {
  background-color: rgba(225, 112, 85, 0.1);
  color: var(--danger);
}

.document-content {
  padding: 1.25rem;
  flex-grow: 1;
}

.document-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.document-author {
  font-size: 0.9rem;
  color: var(--text-light);
  margin: 0 0 1rem 0;
}

.document-description {
  font-size: 0.9rem;
  line-height: 1.5;
  color: var(--text);
  margin-bottom: 1.25rem;
}

.document-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.document-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.document-author {
  font-size: 0.9rem;
  color: var(--text-light);
  margin: 0 0 1rem 0;
}

.document-description {
  font-size: 0.9rem;
  line-height: 1.5;
  color: var(--text);
  margin-bottom: 1.25rem;
}

.document-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.85rem;
}

/* Action Buttons - Fixed Size */
.document-actions {
  display: grid;
  grid-template-columns: auto auto auto;
  gap: 0.1rem;
  padding: 0.5rem;
  border-top: 1px solid #eee;
  background-color: #f8f9fa;
}

.btn-secondary,
.btn-primary,
.btn-danger {
  width: 100%;
  height: 38px;
  padding: 0 0.5rem;
  border: none;
  border-radius: 6px !important;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
  box-sizing: border-box;
}

.btn-secondary {
  background-color: #e0e0e0;
  color: var(--text);
}

.btn-secondary:hover {
  background-color: #d0d0d0;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-light);
}

.btn-danger {
  background-color: var(--danger);
  color: white;
  
}

.btn-danger:hover {
  background-color: #d63031;
}

/* Enhanced Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.75rem;
  margin-top: 2rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* Dark mode override for pagination */
.pagination.transition-colors {
  background-color: inherit !important;
  border-color: inherit !important;
}

.pagination-btn {
  padding: 0.5rem 1rem;
  background-color: white;
  border: 1px solid #dfe6e9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

/* Dark mode override for pagination buttons */
.pagination-btn.transition-colors {
  background-color: inherit !important;
  color: inherit !important;
  border-color: inherit !important;
}

.pagination-btn:hover:not(:disabled) {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
  transform: translateY(-1px);
}

.pagination-btn:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.pagination-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  margin: 0 1rem;
  text-align: center;
}

.page-numbers {
  font-size: 1rem;
  color: var(--dark);
}

.page-numbers.clickable {
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.page-numbers.clickable:hover {
  background-color: var(--primary-light);
  color: white;
}

.items-info {
  font-size: 0.85rem;
  color: var(--text-light);
}

/* Dark mode override for pagination text */
.page-numbers.transition-colors,
.items-info.transition-colors {
  color: inherit !important;
}

.pagination-info.single-page {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  text-align: center;
}

/* Dark mode override for single page pagination */
.pagination-info.single-page.transition-colors {
  background-color: inherit !important;
  border-color: inherit !important;
}

/* Page Jump Feature */
.page-jump {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.page-jump-input {
  width: 60px;
  padding: 0.25rem 0.5rem;
  border: 1px solid #dfe6e9;
  border-radius: 4px;
  text-align: center;
  font-size: 0.9rem;
}

.page-jump-btn {
  padding: 0.25rem 0.75rem;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.page-jump-btn:hover {
  background-color: var(--primary-light);
}

.page-jump-cancel {
  padding: 0.25rem 0.5rem;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.page-jump-cancel:hover {
  background-color: #545b62;
}

/* Dark mode override for page jump elements */
.page-jump-input.transition-colors {
  background-color: inherit !important;
  color: inherit !important;
  border-color: inherit !important;
}

.page-jump-btn.transition-colors,
.page-jump-cancel.transition-colors {
  background-color: inherit !important;
  color: inherit !important;
}

/* States */
.loading {
  text-align: center;
  padding: 2rem;
  font-size: 1.1rem;
  color: var(--text-light);
}

.error-message {
  padding: 1rem;
  background-color: rgba(225, 112, 85, 0.1);
  color: var(--danger);
  border-radius: 6px;
  margin-bottom: 1.5rem;
  text-align: center;
  border-left: 3px solid var(--danger);
}

.no-documents {
  text-align: center;
  padding: 2rem;
  grid-column: 1 / -1;
  color: var(--text-light);
}

/* States */
.loading {
  text-align: center;
  padding: 2rem;
  font-size: 1.1rem;
  color: var(--text-light);
}

.error-message {
  padding: 1rem;
  background-color: rgba(225, 112, 85, 0.1);
  color: var(--danger);
  border-radius: 6px;
  margin-bottom: 1.5rem;
  text-align: center;
  border-left: 3px solid var(--danger);
}

.no-documents {
  text-align: center;
  padding: 2rem;
  grid-column: 1 / -1;
  color: var(--text-light);
}

/* Responsive */
@media (max-width: 768px) {
  .document-card {
    width: calc(50% - 1rem);
  }
}

@media (max-width: 576px) {
  .document-card {
    width: 100%;
  }

  .search-input,
  .filter-input,
  .filter-select,
  .search-btn {
    width: 100%;
  }

  .document-actions {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

/* Document Detail Styles */
.document-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.document-detail {
  background: white;
  border-radius: 12px;
  max-width: 1200px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.document-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e9ecef;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px 12px 0 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.document-detail .document-type-icon {
  font-size: 2.5rem;
  background: white;
  padding: 12px;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-right: 0;
}

.header-left h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
}

.document-type-label {
  background: var(--primary);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: capitalize;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.availability-status .status {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 500;
  font-size: 0.9rem;
}

.availability-status .status.available {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.availability-status .status.unavailable {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.close-btn {
  background: #6c757d;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #5a6268;
  transform: scale(1.1);
}

/* Document Detail Content */
.document-detail-content {
  flex: 1;
  padding: 32px;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 32px;
}

/* Left Column */
.left-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.document-image-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
}

.document-image {
  width: 100%;
  max-width: 250px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.no-image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: #e9ecef;
  border-radius: 8px;
  color: #6c757d;
}

.placeholder-icon {
  font-size: 3rem;
  margin-bottom: 8px;
}

.placeholder-text {
  font-size: 0.9rem;
  font-weight: 500;
}

/* Quick Info Card */
.quick-info-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.quick-info-card h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.quick-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.quick-info-item:last-child {
  border-bottom: none;
}

.quick-label {
  font-weight: 500;
  color: #6c757d;
  font-size: 0.9rem;
}

.quick-value {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.quick-value.barcode {
  font-family: 'Courier New', monospace;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8rem;
}

/* Right Column */
.right-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Detail Sections */
.detail-section {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 2px solid var(--primary);
  padding-bottom: 8px;
}

.description-section {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.description-section h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 2px solid var(--primary);
  padding-bottom: 8px;
}

.description-text {
  line-height: 1.6;
  color: #495057;
  font-size: 0.95rem;
  margin: 0;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-weight: 600;
  color: #6c757d;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item span {
  color: #2c3e50;
  font-size: 0.95rem;
  font-weight: 500;
}

.info-item span.barcode {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.info-item span.location {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
}

/* Keywords */
.keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keyword-tag {
  background: var(--primary);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Document Detail Actions */
.document-detail-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
  padding: 24px 32px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

.document-detail-actions .btn-secondary,
.document-detail-actions .btn-primary,
.document-detail-actions .btn-success {
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.9rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.document-detail-actions .btn-secondary {
  background: #6c757d;
  color: white;
}

.document-detail-actions .btn-secondary:hover {
  background: #5a6268;
}

.document-detail-actions .btn-primary {
  background: var(--primary);
  color: white;
}

.document-detail-actions .btn-primary:hover {
  background: var(--primary-light);
}

.document-detail-actions .btn-success {
  background: var(--success);
  color: white;
}

.document-detail-actions .btn-success:hover {
  background: #00a085;
}

/* Responsive Design for Document Detail */
@media (max-width: 768px) {
  .document-detail {
    margin: 10px;
    max-height: 95vh;
  }

  .document-detail-header {
    padding: 16px 20px;
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-left {
    flex-direction: column;
    gap: 12px;
  }

  .header-left h2 {
    font-size: 1.5rem;
  }

  .document-detail-content {
    padding: 20px;
  }

  .content-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .left-column {
    order: 2;
  }

  .right-column {
    order: 1;
  }

  .document-detail-actions {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
  }

  .document-detail-actions .btn-secondary,
  .document-detail-actions .btn-primary,
  .document-detail-actions .btn-success {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .document-detail-overlay {
    padding: 10px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .quick-info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* Reserve Button Styles */
.btn-reserve {
  background-color: #2563eb;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn-reserve:hover {
  background-color: #1d4ed8;
  transform: translateY(-1px);
}

.btn-reserve:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}