import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { register } from '../../services/authService';
import './Auth.css';

const Register = () => {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'member'
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  // Theme and Language hooks
  const { getCurrentColors, toggleTheme, isDarkMode } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();

  const { username, email, password, confirmPassword, role } = formData;

  const onChange = e => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const validateForm = () => {
    if (!username.trim()) {
      setError('Username is required');
      return false;
    }
    if (username.length < 3) {
      setError('Username must be at least 3 characters long');
      return false;
    }
    if (!email.trim()) {
      setError('Email is required');
      return false;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setError('Please enter a valid email address');
      return false;
    }
    if (!password) {
      setError('Password is required');
      return false;
    }
    if (password.length < 6) {
      setError('Password must be at least 6 characters long');
      return false;
    }
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return false;
    }
    return true;
  };

  const onSubmit = async e => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const registrationData = {
        username: username.trim(),
        email: email.trim().toLowerCase(),
        password,
        role
      };

      await register(registrationData);

      setSuccess('Registration successful! Redirecting to login...');

      // Redirect to login after 2 seconds
      setTimeout(() => {
        navigate('/login', {
          state: {
            message: 'Registration successful! Please log in with your credentials.'
          }
        });
      }, 2000);

    } catch (err) {
      console.error('Registration error:', err);
      setError(err.message || 'Registration failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className="auth-container transition-colors duration-200"
      style={{ backgroundColor: colors.background }}
    >
      {/* Theme Toggle Button */}
      <div className="absolute top-4 right-4 flex items-center space-x-3">
        <button
          onClick={toggleTheme}
          className="p-2 rounded-lg transition-colors duration-200"
          style={{
            backgroundColor: `${colors.primary}20`,
            color: colors.text
          }}
          title={isDarkMode ? t('settings.lightMode') : t('settings.darkMode')}
        >
          {isDarkMode ? '☀️' : '🌙'}
        </button>
      </div>

      <div
        className="auth-card register transition-colors duration-200"
        style={{
          backgroundColor: colors.surface,
          borderColor: colors.border,
          color: colors.text
        }}
      >
        <h2 style={{ color: colors.text }}>
          {t('auth.registerTitle') || 'Register for BiblioAI'}
        </h2>

        {error && (
          <div
            className="alert-error"
            style={{
              backgroundColor: isDarkMode ? '#7f1d1d' : '#f8d7da',
              color: isDarkMode ? '#fecaca' : '#721c24',
              borderColor: isDarkMode ? '#991b1b' : '#f5c6cb'
            }}
          >
            {error}
          </div>
        )}

        {success && (
          <div
            className="alert-error"
            style={{
              backgroundColor: isDarkMode ? '#14532d' : '#d1fae5',
              color: isDarkMode ? '#bbf7d0' : '#065f46',
              borderColor: isDarkMode ? '#166534' : '#a7f3d0'
            }}
          >
            {success}
          </div>
        )}

        <form onSubmit={onSubmit}>
          <div className="form-group">
            <label htmlFor="username" style={{ color: colors.text }}>
              {t('auth.username') || 'Username'}
            </label>
            <input
              type="text"
              id="username"
              name="username"
              value={username}
              onChange={onChange}
              required
              style={{
                backgroundColor: isDarkMode ? '#334155' : '#ffffff',
                borderColor: isDarkMode ? '#475569' : '#ddd',
                color: colors.text
              }}
              placeholder="Enter your username"
            />
          </div>

          <div className="form-group">
            <label htmlFor="email" style={{ color: colors.text }}>
              {t('auth.email') || 'Email'}
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={email}
              onChange={onChange}
              required
              style={{
                backgroundColor: isDarkMode ? '#334155' : '#ffffff',
                borderColor: isDarkMode ? '#475569' : '#ddd',
                color: colors.text
              }}
              placeholder="Enter your email"
            />
          </div>

          <div className="form-group">
            <label htmlFor="password" style={{ color: colors.text }}>
              {t('auth.password') || 'Password'}
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={password}
              onChange={onChange}
              required
              minLength="6"
              style={{
                backgroundColor: isDarkMode ? '#334155' : '#ffffff',
                borderColor: isDarkMode ? '#475569' : '#ddd',
                color: colors.text
              }}
              placeholder="Enter your password (min 6 characters)"
            />
          </div>

          <div className="form-group">
            <label htmlFor="confirmPassword" style={{ color: colors.text }}>
              {t('auth.confirmPassword') || 'Confirm Password'}
            </label>
            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              value={confirmPassword}
              onChange={onChange}
              required
              minLength="6"
              style={{
                backgroundColor: isDarkMode ? '#334155' : '#ffffff',
                borderColor: isDarkMode ? '#475569' : '#ddd',
                color: colors.text
              }}
              placeholder="Confirm your password"
            />
          </div>

          <button
            type="submit"
            className="btn-primary"
            disabled={loading}
            style={{
              backgroundColor: loading ? (isDarkMode ? '#475569' : '#cccccc') : colors.primary,
              color: loading ? (isDarkMode ? '#94a3b8' : '#666') : '#ffffff'
            }}
          >
            {loading ? (t('common.loading') || 'Registering...') : (t('auth.register') || 'Register')}
          </button>
        </form>

        <p className="auth-link" style={{ color: colors.textSecondary }}>
          {t('auth.hasAccount') || 'Already have an account?'}{' '}
          <a
            href="/login"
            style={{ color: colors.primary }}
            onMouseEnter={(e) => e.target.style.color = isDarkMode ? '#93c5fd' : '#3a5a80'}
            onMouseLeave={(e) => e.target.style.color = colors.primary}
          >
            {t('auth.login') || 'Login'}
          </a>
        </p>
      </div>
    </div>
  );
};

export default Register;
