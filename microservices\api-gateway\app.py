from flask import Flask, request, jsonify, Response
from flask_cors import CORS
import requests
import os
import json

app = Flask(__name__)
CORS(app, origins=['http://localhost:3000'], supports_credentials=True)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'api-gateway-secret')
app.config['ALLOWED_EXTENSIONS'] = {'pdf', 'docx', 'doc', 'txt', 'jpg', 'jpeg', 'png'}

# Service URLs
SERVICES = {
    'auth': os.environ.get('AUTH_SERVICE_URL', 'http://localhost:5001'),
    'documents': os.environ.get('DOCUMENT_SERVICE_URL', 'http://localhost:5005'),
    'classification': os.environ.get('CLASSIFICATION_SERVICE_URL', 'http://localhost:5003'),
    'search': os.environ.get('SEARCH_SERVICE_URL', 'http://localhost:5004'),
    'members': os.environ.get('MEMBER_SERVICE_URL', 'http://localhost:5006'),
    'notifications': os.environ.get('NOTIFICATION_SERVICE_URL', 'http://localhost:5007')
}

def forward_request(service_url, path, method='GET', timeout=30):
    """Forward request to appropriate microservice"""
    try:
        # Prepare headers
        headers = {}
        if request.headers.get('Authorization'):
            headers['Authorization'] = request.headers.get('Authorization')
        
        # Handle file uploads differently
        if request.content_type and 'multipart/form-data' in request.content_type:
            files = request.files
            data = request.form.to_dict()
            
            # Prepare files for forwarding
            file_dict = {}
            for file_key, file in files.items():
                if file and file.filename and allowed_file(file.filename):
                    file_dict[file_key] = (file.filename, file.stream, file.content_type)
            
            url = f"{service_url}{path}"
            print(f"[GATEWAY DEBUG] Forwarding {method} with files to {url}")
            
            if method == 'POST':
                response = requests.post(
                    url,
                    headers=headers,
                    data=data,
                    files=file_dict,
                    timeout=timeout
                )
            elif method == 'PUT':
                response = requests.put(
                    url,
                    headers=headers,
                    data=data,
                    files=file_dict,
                    timeout=timeout
                )
            else:
                return jsonify({'error': 'Method not supported for file uploads'}), 405
        else:
            # Original JSON handling
            if request.is_json:
                data = request.get_json()
                headers['Content-Type'] = 'application/json'
            else:
                data = None
            
            url = f"{service_url}{path}"
            print(f"[GATEWAY DEBUG] Forwarding {method} to {url}")

            if method == 'GET':
                response = requests.get(url, headers=headers, params=request.args, timeout=timeout)
            elif method == 'POST':
                response = requests.post(url, headers=headers, json=data, timeout=timeout)
            elif method == 'PUT':
                response = requests.put(url, headers=headers, json=data, timeout=timeout)
            elif method == 'DELETE':
                response = requests.delete(url, headers=headers, timeout=timeout)
            else:
                return jsonify({'message': 'Method not allowed'}), 405

        # Debug response
        print(f"[GATEWAY DEBUG] Response status: {response.status_code}")
        print(f"[GATEWAY DEBUG] Response content: {response.text}")

        # Return response with proper CORS headers
        response_headers = dict(response.headers)
        response_headers['Access-Control-Allow-Origin'] = 'http://localhost:3000'
        response_headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        response_headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        response_headers['Access-Control-Allow-Credentials'] = 'true'

        return Response(
            response.content,
            status=response.status_code,
            headers=response_headers
        )

    except requests.exceptions.Timeout:
        print(f"[GATEWAY ERROR] Timeout connecting to {service_url}{path}")
        return jsonify({'message': 'Service timeout'}), 504
    except requests.exceptions.ConnectionError:
        print(f"[GATEWAY ERROR] Connection error to {service_url}{path}")
        return jsonify({'message': 'Service unavailable'}), 503
    except Exception as e:
        print(f"[GATEWAY ERROR] Exception: {str(e)}")
        return jsonify({'message': f'Gateway error: {str(e)}'}), 500

# Helper functions
def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

# Health check
@app.route('/health', methods=['GET'])
def health_check():
    """Simple health check for gateway"""
    return jsonify({
        'status': 'healthy',
        'service': 'api-gateway',
        'services': list(SERVICES.keys())
    }), 200

# CORS preflight handler for all API routes
@app.route('/api/<path:path>', methods=['OPTIONS'])
def handle_preflight(path):
    response = jsonify({'status': 'ok'})
    response.headers['Access-Control-Allow-Origin'] = 'http://localhost:3000'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
    response.headers['Access-Control-Allow-Credentials'] = 'true'
    return response

# Authentication routes
@app.route('/api/auth/register', methods=['POST'])
def auth_register():
    return forward_request(SERVICES['auth'], '/register', 'POST')

@app.route('/api/auth/login', methods=['POST'])
def auth_login():
    return forward_request(SERVICES['auth'], '/login', 'POST')

@app.route('/api/auth/verify', methods=['GET'])
def auth_verify():
    return forward_request(SERVICES['auth'], '/verify', 'GET')

@app.route('/api/auth/profile', methods=['GET'])
def auth_profile():
    return forward_request(SERVICES['auth'], '/profile', 'GET')

@app.route('/api/auth/users', methods=['GET'])
def auth_users():
    return forward_request(SERVICES['auth'], '/users', 'GET')

# Document routes
@app.route('/api/documents', methods=['GET', 'POST'])
def documents():
    method = request.method
    return forward_request(SERVICES['documents'], '/documents', method)

@app.route('/api/documents/<int:document_id>', methods=['GET', 'PUT', 'DELETE'])
def document_by_id(document_id):
    method = request.method
    print(f"[GATEWAY] Handling {method} request for document {document_id}")
    print(f"[GATEWAY] Content-Type: {request.content_type}")
    print(f"[GATEWAY] Has files: {'Yes' if request.files else 'No'}")
    
    if method == 'PUT':
        # Special handling for PUT requests
        try:
            # Get document service URL
            service_url = SERVICES['documents']
            path = f'/documents/{document_id}'
            
            # Prepare headers
            headers = {}
            if request.headers.get('Authorization'):
                headers['Authorization'] = request.headers.get('Authorization')
            
            # Handle multipart form data
            if request.content_type and 'multipart/form-data' in request.content_type:
                files = request.files
                data = request.form.to_dict()
                
                print(f"[GATEWAY] Forwarding PUT with form data: {data}")
                print(f"[GATEWAY] Files: {[f for f in files]}")
                
                # Prepare files for forwarding
                file_dict = {}
                for file_key, file in files.items():
                    if file and file.filename:
                        file_dict[file_key] = (file.filename, file.stream, file.content_type)
                
                # Forward the request
                response = requests.put(
                    f"{service_url}{path}",
                    headers=headers,
                    data=data,
                    files=file_dict,
                    timeout=30
                )
            else:
                # Handle JSON data
                data = request.get_json() if request.is_json else None
                print(f"[GATEWAY] Forwarding PUT with JSON data: {data}")
                
                response = requests.put(
                    f"{service_url}{path}",
                    headers=headers,
                    json=data,
                    timeout=30
                )
            
            # Return the response from the document service
            return Response(
                response.content,
                status=response.status_code,
                headers=dict(response.headers)
            )
        except Exception as e:
            print(f"[GATEWAY] Error forwarding PUT request: {str(e)}")
            return jsonify({'message': f'Gateway error: {str(e)}'}), 500
    
    # For other methods, use the standard forward_request function
    return forward_request(SERVICES['documents'], f'/documents/{document_id}', method)

@app.route('/api/documents/generate-barcode', methods=['GET'])
def generate_barcode():
    return forward_request(SERVICES['documents'], '/generate-barcode', 'GET')

@app.route('/api/documents/categories', methods=['GET'])
def document_categories():
    return forward_request(SERVICES['documents'], '/categories', 'GET')

@app.route('/api/documents/subjects', methods=['GET'])
def document_subjects():
    return forward_request(SERVICES['documents'], '/subjects', 'GET')

@app.route('/api/documents/statistics', methods=['GET'])
def document_statistics():
    return forward_request(SERVICES['documents'], '/statistics', 'GET')

# Add a specific route for document uploads
@app.route('/api/documents/uploads/<path:filename>')
def forward_document_image(filename):
    """Forward document image requests to the document service"""
    try:
        document_service_url = SERVICES['documents']
        response = requests.get(
            f"{document_service_url}/uploads/{filename}",
            timeout=10
        )
        
        # Return the image with appropriate headers
        return Response(
            response.content,
            status=response.status_code,
            headers={
                'Content-Type': response.headers.get('Content-Type', 'image/jpeg'),
                'Content-Length': response.headers.get('Content-Length', '')
            }
        )
    except Exception as e:
        print(f"[GATEWAY ERROR] Error forwarding image request: {str(e)}")
        return jsonify({'message': f'Error retrieving image: {str(e)}'}), 500

# Search routes
@app.route('/api/search', methods=['GET'])
def search():
    return forward_request(SERVICES['search'], '/search', 'GET')

@app.route('/api/search/suggestions', methods=['GET'])
def search_suggestions():
    return forward_request(SERVICES['search'], '/suggestions', 'GET')

@app.route('/api/search/index', methods=['POST'])
def search_index():
    return forward_request(SERVICES['search'], '/index', 'POST')

@app.route('/api/search/index/<int:document_id>', methods=['DELETE'])
def search_remove_index(document_id):
    return forward_request(SERVICES['search'], f'/index/{document_id}', 'DELETE')

@app.route('/api/search/reindex', methods=['POST'])
def search_reindex():
    return forward_request(SERVICES['search'], '/reindex', 'POST')

@app.route('/api/search/stats', methods=['GET'])
def search_stats():
    return forward_request(SERVICES['search'], '/stats', 'GET')

# Classification routes
@app.route('/api/classification/classify', methods=['POST'])
def classify():
    return forward_request(SERVICES['classification'], '/classify', 'POST')

@app.route('/api/classification/categories', methods=['GET', 'POST'])
def classification_categories():
    method = request.method
    return forward_request(SERVICES['classification'], '/categories', method)

@app.route('/api/classification/subjects', methods=['GET', 'POST'])
def classification_subjects():
    method = request.method
    return forward_request(SERVICES['classification'], '/subjects', method)

@app.route('/api/classification/rules', methods=['GET', 'POST'])
def classification_rules():
    method = request.method
    return forward_request(SERVICES['classification'], '/rules', method)

@app.route('/api/classification/rules/<int:rule_id>', methods=['PUT', 'DELETE'])
def classification_rule_by_id(rule_id):
    method = request.method
    return forward_request(SERVICES['classification'], f'/rules/{rule_id}', method)

# Member service routes
@app.route('/api/members/reservations', methods=['GET', 'POST'])
def member_reservations():
    method = request.method
    return forward_request(SERVICES['members'], '/reservations', method)

@app.route('/api/members/reservations/<int:reservation_id>', methods=['DELETE'])
def member_reservation_by_id(reservation_id):
    return forward_request(SERVICES['members'], f'/reservations/{reservation_id}', 'DELETE')

@app.route('/api/members/borrowings', methods=['GET', 'POST'])
def member_borrowings():
    method = request.method
    return forward_request(SERVICES['members'], '/borrowings', method)

@app.route('/api/members/borrowings/<int:borrowing_id>/return', methods=['PUT'])
def member_return_document(borrowing_id):
    return forward_request(SERVICES['members'], f'/borrowings/{borrowing_id}/return', 'PUT')

@app.route('/api/members/borrowings/<int:borrowing_id>/renew', methods=['PUT'])
def member_renew_borrowing(borrowing_id):
    return forward_request(SERVICES['members'], f'/borrowings/{borrowing_id}/renew', 'PUT')

@app.route('/api/members/history', methods=['GET'])
def member_history():
    return forward_request(SERVICES['members'], '/history', 'GET')

@app.route('/api/members/dashboard', methods=['GET'])
def member_dashboard():
    return forward_request(SERVICES['members'], '/dashboard', 'GET')

@app.route('/api/members/admin/borrowings', methods=['GET'])
def member_admin_borrowings():
    return forward_request(SERVICES['members'], '/admin/borrowings', 'GET')

@app.route('/api/members/admin/overdue', methods=['GET'])
def member_admin_overdue():
    return forward_request(SERVICES['members'], '/admin/overdue', 'GET')

# AI-powered member features
@app.route('/api/members/ai/recommendations', methods=['GET'])
def member_ai_recommendations():
    return forward_request(SERVICES['members'], '/ai/recommendations', 'GET')

@app.route('/api/members/ai/trending', methods=['GET'])
def member_ai_trending():
    return forward_request(SERVICES['members'], '/ai/trending', 'GET')

@app.route('/api/members/ai/chat', methods=['POST'])
def member_ai_chat():
    return forward_request(SERVICES['members'], '/ai/chat', 'POST')

@app.route('/api/members/ai/smart-search', methods=['GET'])
def member_ai_smart_search():
    return forward_request(SERVICES['members'], '/ai/smart-search', 'GET')

# Notification service routes
@app.route('/api/notifications', methods=['GET', 'POST'])
def notifications():
    method = request.method
    return forward_request(SERVICES['notifications'], '/notifications', method)

@app.route('/api/notifications/<int:notification_id>/read', methods=['PUT'])
def notification_mark_read(notification_id):
    return forward_request(SERVICES['notifications'], f'/notifications/{notification_id}/read', 'PUT')

@app.route('/api/notifications/mark-all-read', methods=['PUT'])
def notifications_mark_all_read():
    return forward_request(SERVICES['notifications'], '/notifications/mark-all-read', 'PUT')

@app.route('/api/notifications/<int:notification_id>', methods=['DELETE'])
def notification_delete(notification_id):
    return forward_request(SERVICES['notifications'], f'/notifications/{notification_id}', 'DELETE')

# Enhanced document creation with auto-classification and indexing
@app.route('/api/documents/enhanced', methods=['POST'])
def create_document_enhanced():
    """Create document with automatic classification and search indexing"""
    try:
        # Get request data
        data = request.get_json()
        auth_header = request.headers.get('Authorization')
        headers = {'Authorization': auth_header} if auth_header else {}

        # Step 1: Auto-classify the document
        classification_data = {
            'title': data.get('title', ''),
            'description': data.get('description', '')
        }

        try:
            classify_response = requests.post(
                f"{SERVICES['classification']}/classify",
                headers=headers,
                json=classification_data,
                timeout=10
            )

            if classify_response.status_code == 200:
                classification = classify_response.json()
                # Apply suggestions if not already provided
                if not data.get('category') and classification.get('suggested_category'):
                    data['category'] = classification['suggested_category']
                if not data.get('subject') and classification.get('suggested_subject'):
                    data['subject'] = classification['suggested_subject']
                if not data.get('keywords') and classification.get('suggested_keywords'):
                    data['keywords'] = classification['suggested_keywords']
        except:
            pass  # Continue without classification if service is unavailable

        # Step 2: Create the document
        doc_response = requests.post(
            f"{SERVICES['documents']}/documents",
            headers=headers,
            json=data,
            timeout=15
        )

        if doc_response.status_code != 201:
            return Response(
                doc_response.content,
                status=doc_response.status_code,
                headers=dict(doc_response.headers)
            )

        document = doc_response.json()['document']

        # Step 3: Index the document for search
        try:
            index_data = {
                'document_id': document['id'],
                'title': document['title'],
                'description': document.get('description'),
                'document_type': document.get('document_type'),
                'category': document.get('category'),
                'subject': document.get('subject'),
                'author': document.get('author'),
                'keywords': document.get('keywords'),
                'language': document.get('language'),
                'created_at': document.get('created_at')
            }

            requests.post(
                f"{SERVICES['search']}/index",
                headers=headers,
                json=index_data,
                timeout=10
            )
        except:
            pass  # Continue even if indexing fails

        return jsonify({
            'message': 'Document created successfully with auto-classification and indexing',
            'document': document
        }), 201

    except Exception as e:
        return jsonify({'message': f'Error creating enhanced document: {str(e)}'}), 500

# Service discovery endpoint
@app.route('/api/services', methods=['GET'])
def get_services():
    """Get information about available services"""
    return jsonify({
        'services': list(SERVICES.keys()),
        'endpoints': {
            'auth': ['/register', '/login', '/verify', '/profile', '/users'],
            'documents': ['/documents', '/documents/{id}', '/generate-barcode', '/categories', '/subjects'],
            'search': ['/search', '/suggestions', '/index', '/reindex', '/stats'],
            'classification': ['/classify', '/categories', '/subjects', '/rules'],
            'members': ['/reservations', '/borrowings', '/history', '/dashboard', '/admin/borrowings', '/admin/overdue'],
            'notifications': ['/notifications', '/notifications/{id}/read', '/notifications/mark-all-read']
        }
    }), 200

# Debug endpoint to list all routes
@app.route('/api/debug/routes', methods=['GET'])
def debug_routes():
    """Debug endpoint to list all registered routes"""
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append({
            'endpoint': rule.endpoint,
            'methods': list(rule.methods),
            'rule': str(rule)
        })
    return jsonify({'routes': routes}), 200

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'message': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'message': 'Internal gateway error'}), 500

# Request logging middleware
@app.before_request
def log_request():
    print(f"[GATEWAY] {request.method} {request.path} from {request.remote_addr}")

@app.after_request
def log_response(response):
    print(f"[GATEWAY] Response: {response.status_code}")
    # Ensure all responses have CORS headers
    response.headers['Access-Control-Allow-Origin'] = 'http://localhost:3000'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
    response.headers['Access-Control-Allow-Credentials'] = 'true'
    return response

if __name__ == '__main__':
    print("🚀 Starting API Gateway...")
    print("Available services:")
    for name, url in SERVICES.items():
        print(f"  {name}: {url}")

    print("\nAPI Gateway will be available at:")
    print("  http://localhost:5000")
    print("  http://127.0.0.1:5000")
    print("\nEndpoints:")
    print("  GET  /health")
    print("  POST /api/auth/login")
    print("  POST /api/auth/register")
    print("\n" + "="*50)

    app.run(debug=True, host='0.0.0.0', port=5000)
