import React from 'react';
import Card from '../../components/common/Card';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

const ReviewsSection = () => {
  // Theme and Language hooks
  const { getCurrentColors } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();
  const reviews = [
    {
      id: 1,
      name: "<PERSON>",
      role: "College Student",
      image: "/images/img_cover_1.png",
      review: "Engaging plot, vivid characters; a captivating read that lingers in your thoughts."
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "School Student",
      image: "/images/img_cover_149x149.png",
      review: "Thought-provoking narrative and rich prose. A must-read for any avid book lover!"
    },
    {
      id: 3,
      name: "<PERSON><PERSON><PERSON>",
      role: "ERP Developer",
      image: "/images/img_cover.png",
      review: "Immersive storytelling! An enriching literary experience worth savoring and sharing."
    }
  ];

  return (
    <section id="reviews" className="py-16 px-4 md:px-8 lg:px-20 transition-colors duration-200">
      <div className="mb-12">
        <span
          className="text-lg font-extrabold tracking-wider transition-colors duration-200"
          style={{ color: colors.primary }}
        >
          {t('reviews.title') || 'REVIEWS'}
        </span>
        <h2
          className="text-4xl font-extrabold mt-2 transition-colors duration-200"
          style={{ color: colors.text }}
        >
          💬• {t('reviews.subtitle') || 'Reviews of Others'}
        </h2>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-10">
        {reviews.map((review) => (
          <Card
            key={review.id}
            className="p-10 flex flex-col items-center transition-colors duration-200"
            hasBorder={true}
            hasShadow={true}
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border
            }}
          >
            <div className="w-[149px] h-[149px] rounded-full overflow-hidden mb-8">
              <img
                src={review.image}
                alt={review.name}
                className="w-full h-full object-cover rounded-full"
              />
            </div>

            <p
              className="text-base text-center leading-[30px] mb-8 transition-colors duration-200"
              style={{ color: colors.textSecondary }}
            >
              {review.review}
            </p>

            <h4
              className="text-base font-medium mb-2 transition-colors duration-200"
              style={{ color: colors.primary }}
            >
              {review.name}
            </h4>
            <p
              className="text-base font-medium transition-colors duration-200"
              style={{ color: colors.text }}
            >
              {review.role}
            </p>
          </Card>
        ))}
      </div>
    </section>
  );
};

export default ReviewsSection;