import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

const ServicesSection = () => {
  // Theme and Language hooks
  const { getCurrentColors } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();
  return (
    <section
      id="services"
      className="py-16 px-4 md:px-8 lg:px-20 transition-colors duration-200"
    >
      <div className="mb-12">
        <span
          className="text-lg font-extrabold tracking-wider transition-colors duration-200"
          style={{ color: colors.primary }}
        >
          {t('services.title') || 'SERVICES'}
        </span>
        <h2
          className="text-4xl font-extrabold mt-2 transition-colors duration-200"
          style={{ color: colors.text }}
        >
          🚀• {t('services.subtitle') || 'The Services for You'}
        </h2>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 mt-10">
        <div>
          <img 
            src="/images/img_service.png" 
            alt="Library service" 
            className="w-full max-w-[600px] rounded-[10px]" 
          />
        </div>
        
        <div>
          <h3
            className="text-3xl font-bold transition-colors duration-200"
            style={{ color: colors.text }}
          >
            <span style={{ color: colors.primary }}>
              {t('services.rent') || 'Rent'}
            </span> {t('services.favoriteBook') || 'your favorite book'}
            <br />{t('services.fairlyEasy') || 'fairly easy on'} <span style={{ color: colors.primary }}>BiblioSmart</span>!
          </h3>

          <p
            className="mt-6 text-base leading-relaxed transition-colors duration-200"
            style={{ color: colors.textSecondary }}
          >
            {t('services.description1') || 'Viewing, rent, and organize your favorite books has never been easier. An integrated digital library rent that\'s simple to use, BiblioSmart lets you spend less time managing your work and more time actually doing it!'}
          </p>

          <p
            className="mt-6 text-base leading-relaxed transition-colors duration-200"
            style={{ color: colors.textSecondary }}
          >
            {t('services.description2') || 'Effortless rentals, personalized shelves—BiblioSmart transforms book management, enhancing your reading experience~'}
          </p>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;