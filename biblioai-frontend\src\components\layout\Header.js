import React from 'react';
import { Link } from 'react-router-dom';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

const Header = () => {
  const { getCurrentColors, toggleTheme, isDarkMode } = useTheme();
  const { t, changeLanguage, currentLanguage } = useLanguage();
  const colors = getCurrentColors();

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <header
      className="py-5 px-4 md:px-8 lg:px-20 flex justify-between items-center transition-colors duration-200"
      style={{ backgroundColor: colors.background }}
    >
      <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
      </link>
      <div className="flex items-center">
        <img
          src="/images/img_logo.png"
          alt="BiblioSmart Logo"
          className="w-7 h-7 rounded-[14px]"
        />
        <span
          className="ml-2 text-lg font-bold transition-colors duration-200"
          style={{ color: colors.text }}
        >
          BiblioSmart
        </span>
      </div>
      
      <nav className="hidden md:flex space-x-10">
        <button
          onClick={() => scrollToSection('features')}
          className="text-sm capitalize hover:text-[#4475f2] transition-colors duration-200"
          style={{ color: colors.primary }}
        >
          {t('nav.features') || 'Feature'}
        </button>
        <button
          onClick={() => scrollToSection('services')}
          className="text-sm capitalize hover:text-[#4475f2] transition-colors duration-200"
          style={{ color: colors.textSecondary }}
        >
          {t('nav.services') || 'Service'}
        </button>
        <button
          onClick={() => scrollToSection('reviews')}
          className="text-sm capitalize hover:text-[#4475f2] transition-colors duration-200"
          style={{ color: colors.textSecondary }}
        >
          {t('nav.reviews') || 'Review'}
        </button>
        <button
          onClick={() => scrollToSection('location')}
          className="text-sm capitalize hover:text-[#4475f2] transition-colors duration-200"
          style={{ color: colors.textSecondary }}
        >
          {t('nav.location') || 'Location'}
        </button>
      </nav>
      
      <div className="flex items-center space-x-4">
        {/* Theme Toggle */}
        <button
          onClick={toggleTheme}
          className="p-2 rounded-lg transition-colors duration-200 hover:bg-opacity-10"
          style={{
            backgroundColor: `${colors.primary}20`,
            color: colors.text
          }}
          title={isDarkMode ? t('settings.lightMode') : t('settings.darkMode')}
        >
          {isDarkMode ? '☀️' : '🌙'}
        </button>

        {/* Language Toggle */}
        <button
          onClick={() => changeLanguage(currentLanguage === 'en' ? 'fr' : 'en')}
          className="p-2 rounded-lg transition-colors duration-200 hover:bg-opacity-10 text-sm font-medium"
          style={{
            backgroundColor: `${colors.primary}20`,
            color: colors.text
          }}
          title={t('settings.changeLanguage')}
        >
          {currentLanguage === 'en' ? '🇫🇷 FR' : '🇺🇸 EN'}
        </button>

        {/* Login Button */}
        <Link to="/login">
          <button
            className="px-6 py-2.5 rounded-[10px] text-base font-bold text-white transition-colors duration-300"
            style={{ backgroundColor: colors.primary }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#3461d1'}
            onMouseLeave={(e) => e.target.style.backgroundColor = colors.primary}
          >
            {t('auth.login') || 'Login'}
          </button>
        </Link>
      </div>
    </header>
  );
};

export default Header;