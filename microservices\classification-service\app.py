from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
import os
import requests
import re
from datetime import datetime

app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'classification-service-secret')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get(
    'DATABASE_URI',
    'mysql+pymysql://root:@localhost:3306/biblioai_classification'  # XAMPP MySQL configuration
)
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['AUTH_SERVICE_URL'] = os.environ.get('AUTH_SERVICE_URL', 'http://localhost:5001')

# Initialize extensions
db = SQLAlchemy(app)
CORS(app)

# Classification Models
class Category(db.Model):
    __tablename__ = 'categories'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.Text)
    parent_id = db.Column(db.Integer, db.ForeignKey('categories.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Self-referential relationship for hierarchical categories
    children = db.relationship('Category', backref=db.backref('parent', remote_side=[id]))

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'parent_id': self.parent_id,
            'children': [child.to_dict() for child in self.children]
        }

class Subject(db.Model):
    __tablename__ = 'subjects'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.Text)
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    category = db.relationship('Category', backref='subjects')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category_id': self.category_id,
            'category_name': self.category.name if self.category else None
        }

class ClassificationRule(db.Model):
    __tablename__ = 'classification_rules'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    pattern = db.Column(db.String(255), nullable=False)  # Regex pattern
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'))
    subject_id = db.Column(db.Integer, db.ForeignKey('subjects.id'))
    priority = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    category = db.relationship('Category')
    subject = db.relationship('Subject')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'pattern': self.pattern,
            'category_id': self.category_id,
            'subject_id': self.subject_id,
            'priority': self.priority,
            'is_active': self.is_active,
            'category_name': self.category.name if self.category else None,
            'subject_name': self.subject.name if self.subject else None
        }

# Authentication middleware
def verify_token():
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None

    token = auth_header.split(' ')[1]
    try:
        response = requests.get(
            f"{app.config['AUTH_SERVICE_URL']}/verify",
            headers={'Authorization': f'Bearer {token}'},
            timeout=5
        )
        if response.status_code == 200:
            return response.json()['user']
    except:
        pass
    return None

def require_auth(f):
    def wrapper(*args, **kwargs):
        user = verify_token()
        if not user:
            return jsonify({'message': 'Authentication required'}), 401
        request.current_user = user
        return f(*args, **kwargs)
    wrapper.__name__ = f.__name__
    return wrapper

def require_role(allowed_roles):
    def decorator(f):
        def wrapper(*args, **kwargs):
            user = getattr(request, 'current_user', None)
            if not user or user['role'] not in allowed_roles:
                return jsonify({'message': 'Insufficient permissions'}), 403
            return f(*args, **kwargs)
        wrapper.__name__ = f.__name__
        return wrapper
    return decorator

# Routes
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'service': 'classification-service'}), 200

@app.route('/classify', methods=['POST'])
@require_auth
def classify_document():
    """Automatically classify a document based on its content"""
    data = request.get_json()

    required_fields = ['title']
    if not all(field in data for field in required_fields):
        return jsonify({'message': 'Missing required fields'}), 400

    title = data.get('title', '').lower()
    description = data.get('description', '').lower()
    content = f"{title} {description}"

    # Get active classification rules ordered by priority
    rules = ClassificationRule.query.filter_by(is_active=True).order_by(
        ClassificationRule.priority.desc()
    ).all()

    suggested_category = None
    suggested_subject = None
    matched_rule = None

    # Apply classification rules
    for rule in rules:
        try:
            if re.search(rule.pattern.lower(), content):
                if rule.category:
                    suggested_category = rule.category.name
                if rule.subject:
                    suggested_subject = rule.subject.name
                matched_rule = rule.name
                break
        except re.error:
            continue

    # Fallback: simple keyword-based classification
    if not suggested_category:
        keyword_categories = {
            'fiction': ['novel', 'story', 'fiction', 'romance', 'mystery', 'thriller'],
            'science': ['science', 'physics', 'chemistry', 'biology', 'research'],
            'technology': ['computer', 'programming', 'software', 'technology', 'digital'],
            'history': ['history', 'historical', 'ancient', 'medieval', 'war'],
            'education': ['education', 'learning', 'teaching', 'school', 'university'],
            'business': ['business', 'management', 'economics', 'finance', 'marketing']
        }

        for category, keywords in keyword_categories.items():
            if any(keyword in content for keyword in keywords):
                suggested_category = category.title()
                break

    # Generate keywords from title and description
    suggested_keywords = []
    words = re.findall(r'\b\w{4,}\b', content)
    common_words = {'this', 'that', 'with', 'have', 'will', 'from', 'they', 'been', 'said', 'each', 'which', 'their'}
    suggested_keywords = [word for word in set(words) if word not in common_words][:10]

    return jsonify({
        'suggested_category': suggested_category,
        'suggested_subject': suggested_subject,
        'suggested_keywords': ', '.join(suggested_keywords),
        'matched_rule': matched_rule,
        'confidence': 0.8 if matched_rule else 0.5
    }), 200

@app.route('/categories', methods=['GET'])
@require_auth
def get_categories():
    """Get all categories"""
    categories = Category.query.filter_by(parent_id=None).all()
    return jsonify([cat.to_dict() for cat in categories]), 200

@app.route('/categories', methods=['POST'])
@require_auth
@require_role(['librarian', 'manager'])
def create_category():
    """Create a new category"""
    data = request.get_json()

    if 'name' not in data:
        return jsonify({'message': 'Category name is required'}), 400

    if Category.query.filter_by(name=data['name']).first():
        return jsonify({'message': 'Category already exists'}), 409

    try:
        category = Category(
            name=data['name'],
            description=data.get('description'),
            parent_id=data.get('parent_id')
        )
        db.session.add(category)
        db.session.commit()

        return jsonify({
            'message': 'Category created successfully',
            'category': category.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error creating category: {str(e)}'}), 500

@app.route('/subjects', methods=['GET'])
@require_auth
def get_subjects():
    """Get all subjects"""
    category_id = request.args.get('category_id', type=int)

    query = Subject.query
    if category_id:
        query = query.filter_by(category_id=category_id)

    subjects = query.all()
    return jsonify([subj.to_dict() for subj in subjects]), 200

@app.route('/subjects', methods=['POST'])
@require_auth
@require_role(['librarian', 'manager'])
def create_subject():
    """Create a new subject"""
    data = request.get_json()

    if 'name' not in data:
        return jsonify({'message': 'Subject name is required'}), 400

    if Subject.query.filter_by(name=data['name']).first():
        return jsonify({'message': 'Subject already exists'}), 409

    try:
        subject = Subject(
            name=data['name'],
            description=data.get('description'),
            category_id=data.get('category_id')
        )
        db.session.add(subject)
        db.session.commit()

        return jsonify({
            'message': 'Subject created successfully',
            'subject': subject.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error creating subject: {str(e)}'}), 500

@app.route('/rules', methods=['GET'])
@require_auth
@require_role(['librarian', 'manager'])
def get_classification_rules():
    """Get all classification rules"""
    rules = ClassificationRule.query.order_by(ClassificationRule.priority.desc()).all()
    return jsonify([rule.to_dict() for rule in rules]), 200

@app.route('/rules', methods=['POST'])
@require_auth
@require_role(['librarian', 'manager'])
def create_classification_rule():
    """Create a new classification rule"""
    data = request.get_json()

    required_fields = ['name', 'pattern']
    if not all(field in data for field in required_fields):
        return jsonify({'message': 'Missing required fields'}), 400

    # Validate regex pattern
    try:
        re.compile(data['pattern'])
    except re.error:
        return jsonify({'message': 'Invalid regex pattern'}), 400

    try:
        rule = ClassificationRule(
            name=data['name'],
            pattern=data['pattern'],
            category_id=data.get('category_id'),
            subject_id=data.get('subject_id'),
            priority=data.get('priority', 0),
            is_active=data.get('is_active', True)
        )
        db.session.add(rule)
        db.session.commit()

        return jsonify({
            'message': 'Classification rule created successfully',
            'rule': rule.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error creating rule: {str(e)}'}), 500

@app.route('/rules/<int:rule_id>', methods=['PUT'])
@require_auth
@require_role(['librarian', 'manager'])
def update_classification_rule(rule_id):
    """Update a classification rule"""
    rule = ClassificationRule.query.get(rule_id)
    if not rule:
        return jsonify({'message': 'Rule not found'}), 404

    data = request.get_json()

    # Validate regex pattern if provided
    if 'pattern' in data:
        try:
            re.compile(data['pattern'])
        except re.error:
            return jsonify({'message': 'Invalid regex pattern'}), 400

    try:
        for key, value in data.items():
            if hasattr(rule, key):
                setattr(rule, key, value)

        db.session.commit()
        return jsonify({
            'message': 'Rule updated successfully',
            'rule': rule.to_dict()
        }), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error updating rule: {str(e)}'}), 500

@app.route('/rules/<int:rule_id>', methods=['DELETE'])
@require_auth
@require_role(['librarian', 'manager'])
def delete_classification_rule(rule_id):
    """Delete a classification rule"""
    rule = ClassificationRule.query.get(rule_id)
    if not rule:
        return jsonify({'message': 'Rule not found'}), 404

    try:
        db.session.delete(rule)
        db.session.commit()
        return jsonify({'message': 'Rule deleted successfully'}), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error deleting rule: {str(e)}'}), 500

# Initialize database with default data
def init_default_data():
    """Initialize default categories and subjects"""
    if Category.query.count() == 0:
        default_categories = [
            {'name': 'Fiction', 'description': 'Fictional literature and novels'},
            {'name': 'Non-Fiction', 'description': 'Factual and educational content'},
            {'name': 'Science', 'description': 'Scientific publications and research'},
            {'name': 'Technology', 'description': 'Technology and computer science'},
            {'name': 'History', 'description': 'Historical documents and books'},
            {'name': 'Education', 'description': 'Educational materials and textbooks'},
            {'name': 'Business', 'description': 'Business and economics'},
            {'name': 'Arts', 'description': 'Arts, music, and culture'}
        ]

        for cat_data in default_categories:
            category = Category(**cat_data)
            db.session.add(category)

        db.session.commit()

        # Add default subjects
        default_subjects = [
            {'name': 'Literature', 'category_id': 1},
            {'name': 'Computer Science', 'category_id': 4},
            {'name': 'Mathematics', 'category_id': 3},
            {'name': 'Physics', 'category_id': 3},
            {'name': 'Management', 'category_id': 7},
            {'name': 'World History', 'category_id': 5}
        ]

        for subj_data in default_subjects:
            subject = Subject(**subj_data)
            db.session.add(subject)

        db.session.commit()

# Initialize database
with app.app_context():
    db.create_all()
    init_default_data()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5003)
