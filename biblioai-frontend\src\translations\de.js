const translations = {
  // Allgemein
  common: {
    loading: 'Laden...',
    error: '<PERSON><PERSON>',
    success: 'Erfolg',
    cancel: 'Abbrechen',
    confirm: 'Bestätigen',
    save: '<PERSON><PERSON><PERSON><PERSON>',
    edit: '<PERSON><PERSON><PERSON>',
    delete: '<PERSON><PERSON><PERSON>',
    search: 'Such<PERSON>',
    filter: 'Filtern',
    sort: 'Sortieren',
    refresh: 'Aktualisieren',
    back: '<PERSON>ur<PERSON>',
    next: '<PERSON><PERSON>',
    previous: 'Vorher<PERSON>',
    close: '<PERSON><PERSON><PERSON>ßen',
    open: '<PERSON><PERSON><PERSON>',
    yes: 'Ja',
    no: 'Nein',
    ok: 'OK',
    apply: 'Anwenden',
    reset: '<PERSON><PERSON><PERSON>setzen',
    clear: '<PERSON><PERSON><PERSON>'
  },

  // Navigation
  nav: {
    home: 'Startseite',
    dashboard: 'Dashboard',
    books: '<PERSON><PERSON><PERSON>',
    members: 'Mitglieder',
    reports: 'Berich<PERSON>',
    settings: 'Einstellungen',
    profile: 'Profil',
    logout: 'Abmelden',
    login: 'Anmelden',
    register: 'Registrieren'
  },

  // Authentifizierung
  auth: {
    login: '<PERSON><PERSON><PERSON>',
    register: '<PERSON><PERSON>ere<PERSON>',
    logout: 'Ab<PERSON><PERSON>',
    username: '<PERSON><PERSON><PERSON><PERSON>',
    password: 'Passwort',
    email: 'E-Mail',
    confirmPassword: 'Passwort bestätigen',
    forgotPassword: 'Passwort vergessen?',
    rememberMe: 'Angemeldet bleiben',
    loginSuccess: 'Anmeldung erfolgreich',
    loginError: 'Ungültige Anmeldedaten',
    registerSuccess: 'Registrierung erfolgreich',
    registerError: 'Registrierung fehlgeschlagen',
    logoutSuccess: 'Erfolgreich abgemeldet',
    invalidCredentials: 'Ungültiger Benutzername oder Passwort',
    accountCreated: 'Konto erfolgreich erstellt',
    passwordMismatch: 'Passwörter stimmen nicht überein',
    emailRequired: 'E-Mail ist erforderlich',
    usernameRequired: 'Benutzername ist erforderlich',
    passwordRequired: 'Passwort ist erforderlich'
  },

  // Dashboard
  dashboard: {
    title: 'Dashboard',
    overview: 'Übersicht',
    statistics: 'Statistiken',
    recentActivity: 'Letzte Aktivität',
    quickActions: 'Schnellaktionen',
    totalBooks: 'Bücher gesamt',
    availableBooks: 'Verfügbare Bücher',
    borrowedBooks: 'Ausgeliehene Bücher',
    totalMembers: 'Mitglieder gesamt',
    activeMembers: 'Aktive Mitglieder',
    overdueBooks: 'Überfällige Bücher',
    reservations: 'Reservierungen',
    notifications: 'Benachrichtigungen'
  },

  // Bücher
  books: {
    title: 'Bücher',
    addBook: 'Buch hinzufügen',
    editBook: 'Buch bearbeiten',
    deleteBook: 'Buch löschen',
    bookTitle: 'Buchtitel',
    author: 'Autor',
    isbn: 'ISBN',
    category: 'Kategorie',
    subject: 'Thema',
    publisher: 'Verlag',
    publishedDate: 'Veröffentlichungsdatum',
    pages: 'Seiten',
    language: 'Sprache',
    description: 'Beschreibung',
    totalCopies: 'Exemplare gesamt',
    availableCopies: 'Verfügbare Exemplare',
    location: 'Standort',
    status: 'Status',
    available: 'Verfügbar',
    borrowed: 'Ausgeliehen',
    reserved: 'Reserviert',
    maintenance: 'Wartung',
    searchBooks: 'Bücher suchen',
    filterByCategory: 'Nach Kategorie filtern',
    sortBy: 'Sortieren nach',
    bookAdded: 'Buch erfolgreich hinzugefügt',
    bookUpdated: 'Buch erfolgreich aktualisiert',
    bookDeleted: 'Buch erfolgreich gelöscht',
    bookNotFound: 'Buch nicht gefunden',
    noBooks: 'Keine Bücher gefunden'
  },

  // Mitglieder
  members: {
    title: 'Mitglieder',
    addMember: 'Mitglied hinzufügen',
    editMember: 'Mitglied bearbeiten',
    deleteMember: 'Mitglied löschen',
    memberName: 'Mitgliedsname',
    memberType: 'Mitgliedstyp',
    studentId: 'Studenten-ID',
    phone: 'Telefon',
    address: 'Adresse',
    joinDate: 'Beitrittsdatum',
    status: 'Status',
    active: 'Aktiv',
    inactive: 'Inaktiv',
    suspended: 'Gesperrt',
    borrowingHistory: 'Ausleihhistorie',
    currentBorrowings: 'Aktuelle Ausleihen',
    reservations: 'Reservierungen',
    fines: 'Gebühren',
    memberAdded: 'Mitglied erfolgreich hinzugefügt',
    memberUpdated: 'Mitglied erfolgreich aktualisiert',
    memberDeleted: 'Mitglied erfolgreich gelöscht',
    memberNotFound: 'Mitglied nicht gefunden',
    noMembers: 'Keine Mitglieder gefunden'
  },

  // Ausleihe
  borrowing: {
    title: 'Ausleihe',
    borrowBook: 'Buch ausleihen',
    returnBook: 'Buch zurückgeben',
    renewBook: 'Buch verlängern',
    borrowDate: 'Ausleihdatum',
    dueDate: 'Fälligkeitsdatum',
    returnDate: 'Rückgabedatum',
    renewalCount: 'Anzahl Verlängerungen',
    fine: 'Gebühr',
    overdue: 'Überfällig',
    returned: 'Zurückgegeben',
    borrowed: 'Ausgeliehen',
    myBorrowings: 'Meine Ausleihen',
    borrowingHistory: 'Ausleihhistorie',
    bookBorrowed: 'Buch erfolgreich ausgeliehen',
    bookReturned: 'Buch erfolgreich zurückgegeben',
    bookRenewed: 'Buch erfolgreich verlängert',
    borrowingFailed: 'Ausleihe fehlgeschlagen',
    returnFailed: 'Rückgabe fehlgeschlagen',
    renewalFailed: 'Verlängerung fehlgeschlagen',
    maxRenewalsReached: 'Maximale Verlängerungen erreicht',
    bookOverdue: 'Buch ist überfällig',
    noBorrowings: 'Keine Ausleihen gefunden'
  },

  // Reservierungen
  reservations: {
    title: 'Reservierungen',
    reserveBook: 'Buch reservieren',
    cancelReservation: 'Reservierung stornieren',
    reservationDate: 'Reservierungsdatum',
    expiryDate: 'Ablaufdatum',
    status: 'Status',
    pending: 'Ausstehend',
    ready: 'Bereit',
    expired: 'Abgelaufen',
    cancelled: 'Storniert',
    myReservations: 'Meine Reservierungen',
    bookReserved: 'Buch erfolgreich reserviert',
    reservationCancelled: 'Reservierung erfolgreich storniert',
    reservationFailed: 'Reservierung fehlgeschlagen',
    reservationExpired: 'Reservierung ist abgelaufen',
    noReservations: 'Keine Reservierungen gefunden'
  },

  // KI-Features
  ai: {
    title: 'KI-Features',
    recommendations: 'KI-Empfehlungen',
    chatAssistant: 'KI-Chat-Assistent',
    smartSearch: 'Intelligente Suche',
    trending: 'Trending Bücher',
    forYou: 'Für Sie',
    basedOnHistory: 'Basierend auf Ihrer Lesehistorie und Ihren Vorlieben',
    popularBooks: 'Beliebteste Bücher der letzten 30 Tage',
    whyRecommended: 'Warum empfohlen:',
    matchesCategory: 'Passt zu Ihrer Lieblingskategorie: {{category}}',
    matchesSubject: 'Passt zu Ihrem Interesse an: {{subject}}',
    favoriteAuthor: 'Von Ihrem Lieblingsautor: {{author}}',
    aiAssistantWelcome: 'Hallo! Ich bin Ihr KI-Bibliotheksassistent. Ich kann Ihnen helfen, Bücher zu finden, Fragen zu Bibliotheksrichtlinien zu beantworten und Sie bei Ihren Ausleihen und Reservierungen zu unterstützen. Wie kann ich Ihnen heute helfen?',
    quickActions: 'Schnellaktionen:',
    findProgrammingBooks: 'Programmierbücher finden',
    checkBorrowings: 'Meine aktuellen Ausleihen prüfen',
    renewBook: 'Wie verlängere ich ein Buch?',
    libraryPolicies: 'Was sind die Bibliotheksrichtlinien?',
    recommendBooks: 'Empfehlen Sie mir Bücher',
    thinking: 'Denke nach...',
    typeMessage: 'Fragen Sie mich alles über die Bibliothek...',
    noRecommendations: 'Beginnen Sie mit dem Ausleihen von Büchern, um personalisierte Empfehlungen zu erhalten!',
    noTrendingData: 'Noch keine Trending-Daten verfügbar.',
    aiError: 'Entschuldigung, ich bin auf einen Fehler gestoßen. Bitte versuchen Sie es erneut.',
    connectionError: 'Entschuldigung, ich habe gerade Verbindungsprobleme. Bitte versuchen Sie es später erneut.'
  },

  // Einstellungen
  settings: {
    title: 'Einstellungen',
    general: 'Allgemein',
    appearance: 'Erscheinungsbild',
    language: 'Sprache',
    notifications: 'Benachrichtigungen',
    privacy: 'Datenschutz',
    security: 'Sicherheit',
    account: 'Konto',
    darkMode: 'Dunkler Modus',
    lightMode: 'Heller Modus',
    theme: 'Design',
    changeLanguage: 'Sprache ändern',
    selectLanguage: 'Sprache auswählen',
    emailNotifications: 'E-Mail-Benachrichtigungen',
    pushNotifications: 'Push-Benachrichtigungen',
    smsNotifications: 'SMS-Benachrichtigungen',
    changePassword: 'Passwort ändern',
    currentPassword: 'Aktuelles Passwort',
    newPassword: 'Neues Passwort',
    confirmNewPassword: 'Neues Passwort bestätigen',
    passwordChanged: 'Passwort erfolgreich geändert',
    settingsSaved: 'Einstellungen erfolgreich gespeichert'
  },

  // Benachrichtigungen
  notifications: {
    title: 'Benachrichtigungen',
    markAsRead: 'Als gelesen markieren',
    markAllAsRead: 'Alle als gelesen markieren',
    deleteNotification: 'Benachrichtigung löschen',
    noNotifications: 'Keine Benachrichtigungen',
    bookDueSoon: 'Buch bald fällig: {{title}}',
    bookOverdue: 'Buch überfällig: {{title}}',
    bookReady: 'Reserviertes Buch bereit: {{title}}',
    reservationExpiring: 'Reservierung läuft ab: {{title}}',
    newBookAvailable: 'Neues Buch verfügbar: {{title}}',
    accountSuspended: 'Ihr Konto wurde gesperrt',
    fineAdded: 'Gebühr hinzugefügt: {{amount}}€',
    reminderDue: 'Erinnerung: Buch morgen fällig',
    notificationRead: 'Benachrichtigung als gelesen markiert',
    allNotificationsRead: 'Alle Benachrichtigungen als gelesen markiert'
  },

  // Fehler
  errors: {
    general: 'Ein Fehler ist aufgetreten',
    networkError: 'Netzwerkfehler. Bitte überprüfen Sie Ihre Verbindung.',
    serverError: 'Serverfehler. Bitte versuchen Sie es später erneut.',
    notFound: 'Ressource nicht gefunden',
    unauthorized: 'Sie sind nicht berechtigt, diese Aktion auszuführen',
    forbidden: 'Zugriff verweigert',
    validationError: 'Bitte überprüfen Sie Ihre Eingabe',
    sessionExpired: 'Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an.',
    fileUploadError: 'Datei-Upload fehlgeschlagen',
    fileSizeError: 'Dateigröße zu groß',
    fileTypeError: 'Ungültiger Dateityp'
  },

  // Erfolgsmeldungen
  success: {
    operationCompleted: 'Vorgang erfolgreich abgeschlossen',
    dataSaved: 'Daten erfolgreich gespeichert',
    dataUpdated: 'Daten erfolgreich aktualisiert',
    dataDeleted: 'Daten erfolgreich gelöscht',
    emailSent: 'E-Mail erfolgreich gesendet',
    passwordReset: 'Passwort erfolgreich zurückgesetzt',
    profileUpdated: 'Profil erfolgreich aktualisiert',
    settingsUpdated: 'Einstellungen erfolgreich aktualisiert'
  }
};

export default translations;
