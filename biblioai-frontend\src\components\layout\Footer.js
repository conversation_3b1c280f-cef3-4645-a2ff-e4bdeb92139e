import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

const Footer = () => {
  // Theme and Language hooks
  const { getCurrentColors } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();
  return (
    <footer className="mt-20">
      <div
        className="py-10 px-4 md:px-8 lg:px-20 transition-colors duration-200"
        style={{ backgroundColor: colors.surface }}
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="flex flex-col items-center md:items-start">
            <h3
              className="text-2xl font-bold mb-4 transition-colors duration-200"
              style={{ color: colors.text }}
            >
              {t('footer.managedBy') || 'Managed By'}
            </h3>
            <div
              className="h-[93px] w-[248px] flex items-center justify-center rounded-lg transition-colors duration-200"
              style={{
                backgroundColor: colors.background,
                border: `2px solid ${colors.border}`
              }}
            >
              <h2
                className="text-3xl font-bold transition-colors duration-200"
                style={{ color: colors.primary }}
              >
                BiblioSmart
              </h2>
            </div>
          </div>
          
          <div className="flex flex-col items-center">
            <h3
              className="text-2xl font-bold mb-4 transition-colors duration-200"
              style={{ color: colors.text }}
            >
              {t('footer.socialMedia') || 'Social Media'}
            </h3>
            <div className="flex space-x-4 mt-2">
              <a
                href="https://twitter.com/BiblioSmart"
                target="_blank"
                rel="noopener noreferrer"
                className="transition-opacity duration-200 hover:opacity-70"
              >
                <img
                  src="/images/img_twitter.svg"
                  alt="Twitter"
                  className="w-10 h-10"
                />
              </a>
              <a
                href="https://instagram.com/BiblioSmart"
                target="_blank"
                rel="noopener noreferrer"
                className="transition-opacity duration-200 hover:opacity-70"
              >
                <img
                  src="/images/img_instagram.svg"
                  alt="Instagram"
                  className="w-10 h-10"
                />
              </a>
              <a
                href="https://facebook.com/BiblioSmart"
                target="_blank"
                rel="noopener noreferrer"
                className="transition-opacity duration-200 hover:opacity-70"
              >
                <img
                  src="/images/img_facebook.svg"
                  alt="Facebook"
                  className="w-10 h-10"
                />
              </a>
            </div>
          </div>
          
          <div className="flex flex-col items-center md:items-end">
            <h3
              className="text-2xl font-bold mb-4 transition-colors duration-200"
              style={{ color: colors.text }}
            >
              {t('footer.slogan') || 'Slogan'}
            </h3>
            <p
              className="text-xl font-medium text-center md:text-right transition-colors duration-200"
              style={{ color: colors.textSecondary }}
            >
              {t('footer.hashtag') || '#RentFavBooks'}
            </p>
          </div>
        </div>
      </div>

      <div
        className="py-[51px] text-center transition-colors duration-200"
        style={{ backgroundColor: colors.primary }}
      >
        <p className="text-sm font-bold text-white">
          {t('footer.copyright') || '© 2024 BiblioSmart. All rights reserved.'}
        </p>
      </div>
    </footer>
  );
};

export default Footer;