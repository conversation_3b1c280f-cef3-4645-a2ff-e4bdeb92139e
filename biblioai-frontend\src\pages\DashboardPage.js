import React from 'react';
import Header from '../components/layout/Header';
import HeroSection from '../components/dashboard/HeroSection';
import FeaturesSection from '../components/dashboard/FeaturesSection';
import ServicesSection from '../components/dashboard/ServicesSection';
import Footer from '../components/layout/Footer';
import QuickRentalsSection from '../components/dashboard/QuickRentalsSection';
import ReviewsSection from '../components/dashboard/ReviewsSection';
import LocationSection from '../components/dashboard/LocationSection';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

const BiblioSmartBookReviewPlatformHomepage = () => {
  const { getCurrentColors } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();

  return (
    <div
      className="min-h-screen overflow-auto transition-colors duration-200"
      style={{ backgroundColor: colors.background }}
    >
      <Header />
      <main className="overflow-auto">
        <HeroSection />
        <FeaturesSection />
        <ServicesSection />
        <QuickRentalsSection />
        <ReviewsSection />
        <LocationSection />
      </main>
      <Footer />
    </div>
  );
};

export default BiblioSmartBookReviewPlatformHomepage;