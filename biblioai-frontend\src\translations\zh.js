const translations = {
  // 通用
  common: {
    loading: '加载中...',
    error: '错误',
    success: '成功',
    cancel: '取消',
    confirm: '确认',
    save: '保存',
    edit: '编辑',
    delete: '删除',
    search: '搜索',
    filter: '筛选',
    sort: '排序',
    refresh: '刷新',
    back: '返回',
    next: '下一个',
    previous: '上一个',
    close: '关闭',
    open: '打开',
    yes: '是',
    no: '否',
    ok: '确定',
    apply: '应用',
    reset: '重置',
    clear: '清除'
  },

  // 导航
  nav: {
    home: '首页',
    dashboard: '仪表板',
    books: '图书',
    members: '会员',
    reports: '报告',
    settings: '设置',
    profile: '个人资料',
    logout: '退出登录',
    login: '登录',
    register: '注册'
  },

  // 身份验证
  auth: {
    login: '登录',
    register: '注册',
    logout: '退出登录',
    username: '用户名',
    password: '密码',
    email: '邮箱',
    confirmPassword: '确认密码',
    forgotPassword: '忘记密码？',
    rememberMe: '记住我',
    loginSuccess: '登录成功',
    loginError: '凭据无效',
    registerSuccess: '注册成功',
    registerError: '注册失败',
    logoutSuccess: '退出登录成功',
    invalidCredentials: '用户名或密码无效',
    accountCreated: '账户创建成功',
    passwordMismatch: '密码不匹配',
    emailRequired: '邮箱是必需的',
    usernameRequired: '用户名是必需的',
    passwordRequired: '密码是必需的'
  },

  // 仪表板
  dashboard: {
    title: '仪表板',
    overview: '概览',
    statistics: '统计',
    recentActivity: '最近活动',
    quickActions: '快速操作',
    totalBooks: '图书总数',
    availableBooks: '可用图书',
    borrowedBooks: '已借图书',
    totalMembers: '会员总数',
    activeMembers: '活跃会员',
    overdueBooks: '逾期图书',
    reservations: '预约',
    notifications: '通知'
  },

  // 图书
  books: {
    title: '图书',
    addBook: '添加图书',
    editBook: '编辑图书',
    deleteBook: '删除图书',
    bookTitle: '图书标题',
    author: '作者',
    isbn: 'ISBN',
    category: '类别',
    subject: '主题',
    publisher: '出版社',
    publishedDate: '出版日期',
    pages: '页数',
    language: '语言',
    description: '描述',
    totalCopies: '总册数',
    availableCopies: '可用册数',
    location: '位置',
    status: '状态',
    available: '可用',
    borrowed: '已借出',
    reserved: '已预约',
    maintenance: '维护中',
    searchBooks: '搜索图书',
    filterByCategory: '按类别筛选',
    sortBy: '排序方式',
    bookAdded: '图书添加成功',
    bookUpdated: '图书更新成功',
    bookDeleted: '图书删除成功',
    bookNotFound: '未找到图书',
    noBooks: '未找到图书'
  },

  // 会员
  members: {
    title: '会员',
    addMember: '添加会员',
    editMember: '编辑会员',
    deleteMember: '删除会员',
    memberName: '会员姓名',
    memberType: '会员类型',
    studentId: '学生ID',
    phone: '电话',
    address: '地址',
    joinDate: '加入日期',
    status: '状态',
    active: '活跃',
    inactive: '非活跃',
    suspended: '暂停',
    borrowingHistory: '借阅历史',
    currentBorrowings: '当前借阅',
    reservations: '预约',
    fines: '罚款',
    memberAdded: '会员添加成功',
    memberUpdated: '会员更新成功',
    memberDeleted: '会员删除成功',
    memberNotFound: '未找到会员',
    noMembers: '未找到会员'
  },

  // 借阅
  borrowing: {
    title: '借阅',
    borrowBook: '借阅图书',
    returnBook: '归还图书',
    renewBook: '续借图书',
    borrowDate: '借阅日期',
    dueDate: '到期日期',
    returnDate: '归还日期',
    renewalCount: '续借次数',
    fine: '罚款',
    overdue: '逾期',
    returned: '已归还',
    borrowed: '已借出',
    myBorrowings: '我的借阅',
    borrowingHistory: '借阅历史',
    bookBorrowed: '图书借阅成功',
    bookReturned: '图书归还成功',
    bookRenewed: '图书续借成功',
    borrowingFailed: '借阅图书失败',
    returnFailed: '归还图书失败',
    renewalFailed: '续借图书失败',
    maxRenewalsReached: '已达到最大续借次数',
    bookOverdue: '图书逾期',
    noBorrowings: '未找到借阅记录'
  },

  // 预约
  reservations: {
    title: '预约',
    reserveBook: '预约图书',
    cancelReservation: '取消预约',
    reservationDate: '预约日期',
    expiryDate: '到期日期',
    status: '状态',
    pending: '待处理',
    ready: '就绪',
    expired: '已过期',
    cancelled: '已取消',
    myReservations: '我的预约',
    bookReserved: '图书预约成功',
    reservationCancelled: '预约取消成功',
    reservationFailed: '预约图书失败',
    reservationExpired: '预约已过期',
    noReservations: '未找到预约记录'
  },

  // AI功能
  ai: {
    title: 'AI功能',
    recommendations: 'AI推荐',
    chatAssistant: 'AI聊天助手',
    smartSearch: '智能搜索',
    trending: '热门图书',
    forYou: '为您推荐',
    basedOnHistory: '基于您的阅读历史和偏好',
    popularBooks: '过去30天最受欢迎的图书',
    whyRecommended: '推荐理由：',
    matchesCategory: '符合您喜欢的类别：{{category}}',
    matchesSubject: '符合您对{{subject}}的兴趣',
    favoriteAuthor: '您喜欢的作者：{{author}}',
    aiAssistantWelcome: '您好！我是您的AI图书馆助手。我可以帮助您找到图书，回答有关图书馆政策的问题，并协助您进行借阅和预约。今天我能为您做些什么？',
    quickActions: '快速操作：',
    findProgrammingBooks: '查找编程图书',
    checkBorrowings: '查看我当前的借阅',
    renewBook: '如何续借图书？',
    libraryPolicies: '图书馆政策是什么？',
    recommendBooks: '为我推荐图书',
    thinking: '思考中...',
    typeMessage: '询问我任何关于图书馆的问题...',
    noRecommendations: '开始借阅图书以获得个性化推荐！',
    noTrendingData: '暂无热门数据。',
    aiError: '抱歉，我遇到了错误。请重试。',
    connectionError: '抱歉，我现在遇到连接问题。请稍后重试。'
  },

  // 设置
  settings: {
    title: '设置',
    general: '常规',
    appearance: '外观',
    language: '语言',
    notifications: '通知',
    privacy: '隐私',
    security: '安全',
    account: '账户',
    darkMode: '深色模式',
    lightMode: '浅色模式',
    theme: '主题',
    changeLanguage: '更改语言',
    selectLanguage: '选择语言',
    emailNotifications: '邮件通知',
    pushNotifications: '推送通知',
    smsNotifications: '短信通知',
    changePassword: '更改密码',
    currentPassword: '当前密码',
    newPassword: '新密码',
    confirmNewPassword: '确认新密码',
    passwordChanged: '密码更改成功',
    settingsSaved: '设置保存成功'
  },

  // 通知
  notifications: {
    title: '通知',
    markAsRead: '标记为已读',
    markAllAsRead: '全部标记为已读',
    deleteNotification: '删除通知',
    noNotifications: '无通知',
    bookDueSoon: '图书即将到期：{{title}}',
    bookOverdue: '图书逾期：{{title}}',
    bookReady: '预约图书已就绪：{{title}}',
    reservationExpiring: '预约即将过期：{{title}}',
    newBookAvailable: '新图书可用：{{title}}',
    accountSuspended: '您的账户已被暂停',
    fineAdded: '已添加罚款：¥{{amount}}',
    reminderDue: '提醒：图书明天到期',
    notificationRead: '通知已标记为已读',
    allNotificationsRead: '所有通知已标记为已读'
  },

  // 错误
  errors: {
    general: '发生错误',
    networkError: '网络错误。请检查您的连接。',
    serverError: '服务器错误。请稍后重试。',
    notFound: '未找到资源',
    unauthorized: '您无权执行此操作',
    forbidden: '访问被禁止',
    validationError: '请检查您的输入',
    sessionExpired: '您的会话已过期。请重新登录。',
    fileUploadError: '文件上传失败',
    fileSizeError: '文件大小过大',
    fileTypeError: '文件类型无效'
  },

  // 成功消息
  success: {
    operationCompleted: '操作成功完成',
    dataSaved: '数据保存成功',
    dataUpdated: '数据更新成功',
    dataDeleted: '数据删除成功',
    emailSent: '邮件发送成功',
    passwordReset: '密码重置成功',
    profileUpdated: '个人资料更新成功',
    settingsUpdated: '设置更新成功'
  }
};

export default translations;
