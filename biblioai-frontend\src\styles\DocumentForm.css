/* DocumentForm.css - Complete Modal Version */

:root {
    /* Color Variables */
    --primary: #6C5CE7;
    --primary-light: #A29BFE;
    --secondary: #00B894;
    --accent: #FD79A8;
    --dark: #2D3436;
    --light: #F5F6FA;
    --card-bg: #FFFFFF;
    --success: #00B894;
    --warning: #FDCB6E;
    --danger: #E17055;
    --text: #2D3436;
    --text-light: #636E72;
    --border: #DFE6E9;
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
  
  /* Modal Overlay */
  .document-form-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(45, 52, 54, 0.7);
    display: flex;
    justify-content: center;
    align-items: flex-start;
    z-index: 1000;
    padding: 2rem;
    overflow-y: auto;
    backdrop-filter: blur(2px);
  }
  
  /* Modal Container */
  .document-form-container {
    background: var(--card-bg);
    border-radius: 12px;
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow);
    animation: fadeIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  /* Form Header */
  .document-form-header {
    position: sticky;
    top: 0;
    background: var(--card-bg);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 10;
  }
  
  .document-form-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: var(--dark);
  }
  
  .close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
    padding: 0.5rem;
    transition: all 0.2s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }
  
  .close-btn:hover {
    color: var(--danger);
    background-color: rgba(225, 112, 85, 0.1);
  }
  
  /* Form Content */
  .document-form-content {
    padding: 0 2rem 2rem;
    flex-grow: 1;
    overflow-y: auto;
  }
  
  /* Form Sections */
  .form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border);
  }
  
  .form-section:last-child {
    border-bottom: none;
    margin-bottom: 1rem;
  }
  
  .form-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 1.25rem 0;
    color: var(--dark);
    position: relative;
    padding-left: 1rem;
  }
  
  .form-section h3::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary);
    border-radius: 2px;
  }
  
  /* Form Groups */
  .form-group {
    margin-bottom: 1.25rem;
    position: relative;
  }
  
  .form-group label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text);
  }
  
  .form-group label::after {
    content: ' *';
    color: var(--danger);
    opacity: 0;
  }
  
  .form-group label.required::after {
    opacity: 1;
  }
  
  .form-row {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.25rem;
  }
  
  .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
  }
  
  /* Input Fields */
  input[type="text"],
  input[type="number"],
  input[type="date"],
  textarea,
  select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border);
    border-radius: 6px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background-color: white;
    font-family: 'Poppins', sans-serif;
  }
  
  input[type="text"]:focus,
  input[type="number"]:focus,
  input[type="date"]:focus,
  textarea:focus,
  select:focus {
    outline: none;
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.1);
  }
  
  textarea {
    min-height: 100px;
    resize: vertical;
  }
  
  select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1em;
  }
  
  /* Title Input with Suggestions */
  .title-input-container {
    position: relative;
  }
  
  .book-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--border);
    border-radius: 0 0 6px 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 10;
    max-height: 300px;
    overflow-y: auto;
    margin-top: -1px;
  }
  
  .suggestion-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid var(--border);
  }
  
  .suggestion-item:last-child {
    border-bottom: none;
  }
  
  .suggestion-item:hover {
    background-color: var(--light);
  }
  
  .suggestion-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
  }
  
  .suggestion-author {
    font-size: 0.85rem;
    color: var(--text-light);
  }
  
  .mini-loader {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border: 2px solid var(--primary-light);
    border-top-color: var(--primary);
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
  }
  
  /* Barcode Input */
  .barcode-input {
    display: flex;
    gap: 0.5rem;
  }
  
  .barcode-input input {
    flex: 1;
  }
  
  .generate-barcode-btn {
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0 1rem;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .generate-barcode-btn:hover {
    background-color: var(--primary-light);
  }
  
  .generate-barcode-btn:disabled {
    background-color: var(--border);
    cursor: not-allowed;
  }
  
  /* Classification Section */
  .auto-classify-section {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: rgba(108, 92, 231, 0.05);
    border-radius: 6px;
    border-left: 3px solid var(--primary);
  }
  
  .classify-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .classify-btn:hover {
    background-color: var(--primary-light);
    transform: translateY(-2px);
  }
  
  .classify-note {
    font-size: 0.85rem;
    color: var(--text-light);
    margin: 0.5rem 0 0 0;
  }
  
  .classification-form {
    padding: 1rem;
    background-color: rgba(108, 92, 231, 0.05);
    border-radius: 6px;
    margin-bottom: 1rem;
  }
  
  .classification-form h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
  }
  
  .classification-form p {
    margin: 0 0 1rem 0;
    color: var(--text-light);
    font-size: 0.9rem;
  }
  
  .loader {
    border: 3px solid var(--light);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 1rem auto;
  }
  
  .classification-result {
    padding: 1rem;
    background-color: rgba(0, 184, 148, 0.05);
    border-radius: 6px;
    margin-bottom: 1rem;
    border-left: 3px solid var(--success);
  }
  
  .classification-result h4 {
    margin: 0 0 0.75rem 0;
    font-size: 1.1rem;
  }
  
  .result-card {
    background-color: white;
    border-radius: 6px;
    padding: 1rem;
    margin: 1rem 0;
    border: 1px solid var(--border);
  }
  
  .result-item {
    display: flex;
    margin-bottom: 0.75rem;
  }
  
  .result-item:last-child {
    margin-bottom: 0;
  }
  
  .result-label {
    font-weight: 600;
    min-width: 100px;
    color: var(--text-light);
  }
  
  .result-value {
    font-weight: 500;
    color: var(--text);
  }
  
  .keywords-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .keyword-tag {
    background-color: var(--primary-light);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
  }
  
  .result-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1rem;
  }
  
  /* Image Preview */
  .image-preview {
    margin-top: 10px;
  }

  .image-preview img {
    max-width: 200px;
    max-height: 200px;
    margin-top: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
  }
  
  /* Form Actions */
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border);
    position: sticky;
    bottom: 0;
    background: var(--card-bg);
    padding-bottom: 1rem;
  }

  /* Dark mode override for form actions */
  .form-actions.transition-colors {
    background: inherit !important;
    border-top-color: inherit !important;
  }
  
  .btn-secondary,
  .btn-primary {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    text-align: center;
  }
  
  .btn-secondary {
    background-color: #e0e0e0;
    color: var(--text);
    border: none;
  }
  
  .btn-secondary:hover {
    background-color: #d0d0d0;
  }
  
  .btn-primary {
    background-color: var(--primary);
    color: white;
    border: none;
  }
  
  .btn-primary:hover {
    background-color: var(--primary-light);
  }
  
  .btn-primary:disabled {
    background-color: var(--border);
    cursor: not-allowed;
  }
  
  /* Error Message */
  .error-message {
    padding: 1rem;
    background-color: rgba(225, 112, 85, 0.1);
    color: var(--danger);
    border-radius: 6px;
    margin-bottom: 1.5rem;
    border-left: 3px solid var(--danger);
    font-weight: 500;
  }
  
  /* Datalist Styles */
  datalist {
    position: absolute;
    background-color: white;
    border: 1px solid var(--border);
    border-radius: 0 0 4px 4px;
    max-height: 200px;
    overflow-y: auto;
  }
  
  /* Animations */
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .document-form-modal-overlay {
      padding: 1rem;
    }
    
    .document-form-container {
      max-height: 95vh;
    }
    
    .document-form-header {
      padding: 1rem;
    }
    
    .document-form-content {
      padding: 0 1rem 1rem;
    }
    
    .form-row {
      flex-direction: column;
      gap: 1rem;
    }
    
    .form-actions {
      flex-direction: column-reverse;
      gap: 0.75rem;
    }
    
    .btn-secondary,
    .btn-primary {
      width: 100%;
    }
  }
  
  /* Body scroll lock */
  body.modal-open {
    overflow: hidden;
  }