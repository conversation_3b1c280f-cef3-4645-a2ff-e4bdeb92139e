/* Statistics Container */
.statistics-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
}

.last-updated {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.refresh-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Loading and Error States */
.loading-state, .error-state, .no-data-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon, .no-data-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.retry-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 16px;
}

/* Overview Section */
.overview-section {
  margin-bottom: 32px;
}

.overview-section h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 600;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  border-left: 4px solid #007bff;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.overview-card.primary { border-left-color: #007bff; }
.overview-card.success { border-left-color: #28a745; }
.overview-card.info { border-left-color: #17a2b8; }
.overview-card.warning { border-left-color: #ffc107; }

.card-icon {
  font-size: 2.5rem;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-content h4 {
  margin: 0 0 8px 0;
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-value {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.card-subtitle {
  margin: 0;
  color: #6c757d;
  font-size: 0.85rem;
}

/* Metrics Section */
.metrics-section {
  margin-bottom: 32px;
}

.metrics-section h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 600;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.metric-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.metric-card h4 {
  margin: 0 0 12px 0;
  color: #6c757d;
  font-size: 0.85rem;
  font-weight: 500;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #007bff;
  margin-bottom: 4px;
}

.metric-subtitle {
  margin: 0;
  color: #6c757d;
  font-size: 0.8rem;
}

/* Charts Section */
.charts-section {
  margin-bottom: 32px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-card h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Bar Chart */
.bar-chart {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.bar-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.bar-label {
  font-size: 0.85rem;
  font-weight: 500;
  color: #495057;
  text-transform: capitalize;
}

.bar-container {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.bar-fill {
  height: 8px;
  background: linear-gradient(90deg, #007bff, #0056b3);
  border-radius: 4px;
  min-width: 4px;
  transition: width 0.3s ease;
}

.bar-value {
  font-size: 0.8rem;
  font-weight: 600;
  color: #495057;
  min-width: 30px;
}

/* List Chart */
.list-chart {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.list-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
}

.list-item:last-child {
  border-bottom: none;
}

.list-rank {
  font-size: 0.8rem;
  font-weight: 600;
  color: #6c757d;
  min-width: 24px;
}

.list-name {
  flex: 1;
  font-size: 0.9rem;
  color: #495057;
}

.list-value {
  font-size: 0.85rem;
  font-weight: 600;
  color: #007bff;
}

/* Pie Chart List */
.pie-chart-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.pie-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px 0;
}

.pie-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.pie-label {
  flex: 1;
  font-size: 0.9rem;
  color: #495057;
}

.pie-value {
  font-size: 0.85rem;
  font-weight: 600;
  color: #495057;
}

/* Popular Section */
.popular-section {
  margin-bottom: 32px;
}

.popular-section h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 600;
}

.popular-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.popular-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.popular-card h4 {
  margin: 0 0 16px 0;
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
}

.popular-name {
  font-size: 1.4rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
}

.popular-count {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Search Statistics */
.search-stats-section {
  margin-bottom: 32px;
}

.search-stats-section h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 600;
}

.search-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.search-stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-stat-card h4 {
  margin: 0 0 16px 0;
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
}

.search-stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #17a2b8;
}

.search-type-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-type-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  border-bottom: 1px solid #f1f3f4;
}

.search-type-item:last-child {
  border-bottom: none;
}

.no-data {
  color: #6c757d;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .statistics-container {
    padding: 16px;
  }
  
  .statistics-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .overview-grid,
  .metrics-grid,
  .charts-grid,
  .popular-grid,
  .search-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .overview-card {
    flex-direction: column;
    text-align: center;
  }
  
  .card-icon {
    margin-bottom: 16px;
  }
}
