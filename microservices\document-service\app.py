from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>, decode_token
from datetime import datetime
import os
import uuid
import requests
from werkzeug.utils import secure_filename
from sqlalchemy import text

app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'document-service-secret')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get(
    'DATABASE_URI',
    'mysql+pymysql://root:@localhost:3306/biblioai_documents'  # XAMPP MySQL configuration
)
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['AUTH_SERVICE_URL'] = os.environ.get('AUTH_SERVICE_URL', 'http://localhost:5001')
# JWT Configuration - Must match auth service
app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET_KEY', 'biblioai-jwt-secret-key-2024')
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['ALLOWED_EXTENSIONS'] = {'png', 'jpg', 'jpeg', 'gif'}
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max

# Initialize extensions
db = SQLAlchemy(app)
jwt = JWTManager(app)
CORS(app)

# Document Models
class Document(db.Model):
    __tablename__ = 'documents'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    document_type = db.Column(db.String(50), nullable=False)
    isbn = db.Column(db.String(20), unique=True, nullable=True)
    barcode = db.Column(db.String(50), unique=True, nullable=False)

    # Classification fields
    category = db.Column(db.String(100))
    subject = db.Column(db.String(100))
    keywords = db.Column(db.Text)

    # Publication details
    publisher = db.Column(db.String(200))
    publication_date = db.Column(db.Date)
    language = db.Column(db.String(50), default='English')

    # Physical details
    pages = db.Column(db.Integer)
    format = db.Column(db.String(50))
    location = db.Column(db.String(100))

    # Image path
    image_path = db.Column(db.String(255))

    # Availability
    total_copies = db.Column(db.Integer, default=1)
    available_copies = db.Column(db.Integer, default=1)

    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, nullable=False)

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'document_type': self.document_type,
            'isbn': self.isbn,
            'barcode': self.barcode,
            'category': self.category,
            'subject': self.subject,
            'keywords': self.keywords,
            'publisher': self.publisher,
            'publication_date': self.publication_date.isoformat() if self.publication_date else None,
            'language': self.language,
            'pages': self.pages,
            'format': self.format,
            'location': self.location,
            'image_path': self.image_path,
            'total_copies': self.total_copies,
            'available_copies': self.available_copies,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'created_by': self.created_by
        }

class Book(Document):
    __tablename__ = 'books'

    id = db.Column(db.Integer, db.ForeignKey('documents.id', ondelete='CASCADE'), primary_key=True)
    author = db.Column(db.String(200), nullable=False)
    edition = db.Column(db.String(50))
    series = db.Column(db.String(200))

    __mapper_args__ = {'polymorphic_identity': 'book'}

    def to_dict(self):
        data = super().to_dict()
        data.update({
            'author': self.author,
            'edition': self.edition,
            'series': self.series
        })
        return data

class Periodical(Document):
    __tablename__ = 'periodicals'

    id = db.Column(db.Integer, db.ForeignKey('documents.id', ondelete='CASCADE'), primary_key=True)
    issn = db.Column(db.String(20))
    volume = db.Column(db.String(20))
    issue = db.Column(db.String(20))
    frequency = db.Column(db.String(50))

    __mapper_args__ = {'polymorphic_identity': 'periodical'}

    def to_dict(self):
        data = super().to_dict()
        data.update({
            'issn': self.issn,
            'volume': self.volume,
            'issue': self.issue,
            'frequency': self.frequency
        })
        return data

# Authentication middleware
def verify_token():
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None

    token = auth_header.split(' ')[1]
    try:
        # Try to decode JWT locally first
        decoded_token = decode_token(token)
        user_id = int(decoded_token['sub'])  # Convert string back to integer
        role = decoded_token.get('role', 'member')
        username = decoded_token.get('username', 'unknown')

        return {
            'id': user_id,
            'role': role,
            'username': username
        }
    except Exception as e:
        print(f"JWT decode error: {e}")
        # Fallback to auth service verification
        try:
            response = requests.get(
                f"{app.config['AUTH_SERVICE_URL']}/verify",
                headers={'Authorization': f'Bearer {token}'},
                timeout=5
            )
            if response.status_code == 200:
                return response.json()['user']
        except Exception as e:
            print(f"Auth service verification error: {e}")
            pass
    return None

def require_auth(f):
    def wrapper(*args, **kwargs):
        user = verify_token()
        if not user:
            return jsonify({'message': 'Authentication required'}), 401
        request.current_user = user
        return f(*args, **kwargs)
    wrapper.__name__ = f.__name__
    return wrapper

def require_role(allowed_roles):
    def decorator(f):
        def wrapper(*args, **kwargs):
            user = getattr(request, 'current_user', None)
            if not user or user['role'] not in allowed_roles:
                return jsonify({'message': 'Insufficient permissions'}), 403
            return f(*args, **kwargs)
        wrapper.__name__ = f.__name__
        return wrapper
    return decorator

# Helper functions
def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

# Routes
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'service': 'document-service'}), 200

@app.route('/documents', methods=['GET'])
@require_auth
def get_documents():
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    search = request.args.get('search', '')
    document_type = request.args.get('type', '')
    category = request.args.get('category', '')
    available_only = request.args.get('available_only', 'false').lower() == 'true'

    print(f"[DOCUMENT SERVICE] Search request - search: '{search}', type: '{document_type}', category: '{category}', available_only: {available_only}")

    query = Document.query

    if search:
        search_term = f"%{search}%"
        print(f"[DOCUMENT SERVICE] Applying search filter with term: '{search_term}'")

        # Join with Book table to search author field
        query = query.outerjoin(Book, Document.id == Book.id)
        query = query.filter(
            db.or_(
                Document.title.ilike(search_term),
                Document.description.ilike(search_term),
                Document.keywords.ilike(search_term),
                Document.publisher.ilike(search_term),
                Book.author.ilike(search_term)  # Search in author field for books
            )
        )

        # Debug: Show what we're searching for
        print(f"[DOCUMENT SERVICE] Search conditions:")
        print(f"  - Title contains: '{search_term}'")
        print(f"  - Description contains: '{search_term}'")
        print(f"  - Keywords contains: '{search_term}'")
        print(f"  - Publisher contains: '{search_term}'")
        print(f"  - Author contains: '{search_term}'")

    if document_type:
        print(f"[DOCUMENT SERVICE] Applying document_type filter: '{document_type}'")
        query = query.filter(Document.document_type == document_type)

    if category:
        print(f"[DOCUMENT SERVICE] Applying category filter: '{category}'")
        query = query.filter(Document.category == category)

    if available_only:
        print(f"[DOCUMENT SERVICE] Applying available_only filter: available_copies > 0")
        query = query.filter(Document.available_copies > 0)

    # Debug: Show the SQL query being executed
    print(f"[DOCUMENT SERVICE] Final SQL query: {str(query.statement.compile(compile_kwargs={'literal_binds': True}))}")

    documents = query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

    print(f"[DOCUMENT SERVICE] Query results: {documents.total} total documents, {len(documents.items)} on this page")

    # Debug: Show what documents were found
    for doc in documents.items:
        if hasattr(doc, 'author'):
            print(f"[DOCUMENT SERVICE] Found: '{doc.title}' by {doc.author} (Type: {doc.document_type})")
        else:
            print(f"[DOCUMENT SERVICE] Found: '{doc.title}' (Type: {doc.document_type})")

    return jsonify({
        'documents': [doc.to_dict() for doc in documents.items],
        'total': documents.total,
        'pages': documents.pages,
        'current_page': page,
        'per_page': per_page
    }), 200

@app.route('/documents/<int:document_id>', methods=['GET'])
@require_auth
def get_document(document_id):
    document = Document.query.get(document_id)
    if not document:
        return jsonify({'message': 'Document not found'}), 404
    return jsonify(document.to_dict()), 200

@app.route('/documents', methods=['POST'])
@require_auth
@require_role(['librarian', 'manager'])
def create_document():
    try:
        # Handle file upload
        image_path = None
        if 'image' in request.files:
            file = request.files['image']
            if file and file.filename and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                # Ensure upload folder exists
                os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
                # Save file with relative path
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(file_path)
                # Store just the filename, not the full path
                image_path = filename
                print(f"[DOCUMENT SERVICE] Saved image to {file_path}")
        
        # Get form data
        # data = request.form.to_dict() if request.files else request.get_json()
        # if not data:
        #     return jsonify({'message': 'No data provided'}), 400

        # Get form or JSON data
        if request.content_type and request.content_type.startswith('multipart/form-data'):
            data = request.form.to_dict()
        else:
            data = request.get_json()

        if not data:
            return jsonify({'message': 'No data provided'}), 400

        if image_path:
            data['image_path'] = image_path

        
        user = request.current_user

        required_fields = ['title', 'document_type', 'barcode']
        if not all(field in data for field in required_fields):
            return jsonify({'message': 'Missing required fields'}), 400

        if Document.query.filter_by(barcode=data['barcode']).first():
            return jsonify({'message': 'Barcode already exists'}), 409

        try:
            document_type = data['document_type']

            if document_type == 'book':
                if 'author' not in data:
                    return jsonify({'message': 'Author is required for books'}), 400
                document = Book(
                    title=data['title'],
                    document_type=document_type,  # Explicitly set document_type
                    barcode=data['barcode'],
                    created_by=user['id'],
                    author=data['author'],
                    **{k: v for k, v in data.items() if k not in ['title', 'barcode', 'author', 'document_type']}
                )
            elif document_type == 'periodical':
                document = Periodical(
                    title=data['title'],
                    document_type=document_type,  # Explicitly set document_type
                    barcode=data['barcode'],
                    created_by=user['id'],
                    **{k: v for k, v in data.items() if k not in ['title', 'barcode', 'document_type']}
                )
            else:
                document = Document(
                    title=data['title'],
                    document_type=document_type,
                    barcode=data['barcode'],
                    created_by=user['id'],
                    **{k: v for k, v in data.items() if k not in ['title', 'barcode', 'document_type']}
                )

            db.session.add(document)
            db.session.commit()

            return jsonify({
                'message': 'Document created successfully',
                'document': document.to_dict()
            }), 201

        except Exception as e:
            db.session.rollback()
            return jsonify({'message': f'Error creating document: {str(e)}'}), 500

    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error creating document: {str(e)}'}), 500

# Add this function to help debug document updates
def log_document_fields(document, prefix=""):
    """Log all fields of a document for debugging purposes"""
    print(f"{prefix} Document fields:")
    for column in document.__table__.columns:
        field_name = column.name
        field_value = getattr(document, field_name)
        print(f"{prefix}   {field_name}: {field_value}")

@app.route('/documents/<int:document_id>', methods=['PUT'])
@require_auth
@require_role(['librarian', 'manager'])
def update_document(document_id):
    print(f"[DOCUMENT SERVICE] Starting update for document ID: {document_id}")
    
    # Verify document exists
    document = Document.query.get(document_id)
    if not document:
        print(f"[DOCUMENT SERVICE] Document with ID {document_id} not found in database")
        return jsonify({'message': 'Document not found'}), 404

    print(f"[DOCUMENT SERVICE] Found document: {document.title} (ID: {document.id})")
    log_document_fields(document, "[DOCUMENT SERVICE] BEFORE UPDATE:")
    
    try:
        # Get form or JSON data
        data = None
        if request.content_type and 'multipart/form-data' in request.content_type:
            print(f"[DOCUMENT SERVICE] Processing multipart form data")
            data = request.form.to_dict()
            print(f"[DOCUMENT SERVICE] Form data received: {data}")
            
            if request.files:
                print(f"[DOCUMENT SERVICE] Files received: {list(request.files.keys())}")
        else:
            print(f"[DOCUMENT SERVICE] Processing JSON data")
            data = request.get_json()
            print(f"[DOCUMENT SERVICE] JSON data received: {data}")
            
        if not data:
            print(f"[DOCUMENT SERVICE] No data provided in request")
            return jsonify({'message': 'No data provided'}), 400
        
        # Handle file upload
        if 'image' in request.files:
            file = request.files['image']
            if file and file.filename and allowed_file(file.filename):
                print(f"[DOCUMENT SERVICE] Processing image: {file.filename}")
                
                # Remove old image if exists
                if document.image_path and os.path.exists(os.path.join(app.config['UPLOAD_FOLDER'], document.image_path)):
                    print(f"[DOCUMENT SERVICE] Removing old image: {document.image_path}")
                    os.remove(os.path.join(app.config['UPLOAD_FOLDER'], document.image_path))
                
                # Save new image
                filename = secure_filename(file.filename)
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
                file.save(filepath)
                document.image_path = filename
                print(f"[DOCUMENT SERVICE] Saved new image: {filename}")
        
        # Remove image_path from data if present to avoid overwriting with form data
        if 'image_path' in data:
            print(f"[DOCUMENT SERVICE] Removing image_path from update data")
            del data['image_path']

        # Remove id from data if present
        if 'id' in data:
            print(f"[DOCUMENT SERVICE] Removing id from update data")
            del data['id']
            
        # Remove created_at and created_by if present
        if 'created_at' in data:
            print(f"[DOCUMENT SERVICE] Removing created_at from update data")
            del data['created_at']
            
        if 'created_by' in data:
            print(f"[DOCUMENT SERVICE] Removing created_by from update data")
            del data['created_by']
            
        # Remove updated_at if present
        if 'updated_at' in data:
            print(f"[DOCUMENT SERVICE] Removing updated_at from update data")
            del data['updated_at']
        
        # Preserve document_type - don't change it during updates
        original_document_type = document.document_type
        print(f"[DOCUMENT SERVICE] Original document_type: {original_document_type}")
        
        if 'document_type' in data:
            if data['document_type'] != original_document_type:
                print(f"[DOCUMENT SERVICE] WARNING: Attempt to change document_type from {original_document_type} to {data['document_type']} - ignoring")
            del data['document_type']
        
        # Track changes for debugging
        changes = {}
        
        # Update document fields
        for key, value in data.items():
            if hasattr(document, key):
                old_value = getattr(document, key)
                print(f"[DOCUMENT SERVICE] Updating field '{key}': '{old_value}' -> '{value}'")
                
                # Convert numeric strings to integers for appropriate fields
                if key in ['pages', 'total_copies', 'available_copies', 'duration'] and value:
                    try:
                        value = int(value)
                    except ValueError:
                        print(f"[DOCUMENT SERVICE] Warning: Could not convert {key}='{value}' to integer")
                
                setattr(document, key, value)
                changes[key] = {'old': str(old_value), 'new': str(value)}
        
        if not changes:
            print(f"[DOCUMENT SERVICE] No fields were updated")
            return jsonify({'message': 'No fields were updated'}), 200
        
        print(f"[DOCUMENT SERVICE] Setting updated_at timestamp")
        document.updated_at = datetime.utcnow()
        
        print(f"[DOCUMENT SERVICE] Committing changes to database")
        db.session.commit()
        print(f"[DOCUMENT SERVICE] Database commit successful")
        
        # Verify the changes were saved by reloading the document
        db.session.refresh(document)
        print(f"[DOCUMENT SERVICE] Refreshed document from database")
        log_document_fields(document, "[DOCUMENT SERVICE] AFTER UPDATE:")
        
        return jsonify({
            'message': 'Document updated successfully',
            'document': document.to_dict(),
            'changes': changes
        }), 200

    except Exception as e:
        db.session.rollback()
        print(f"[DOCUMENT SERVICE] Error updating document: {str(e)}")
        print(f"[DOCUMENT SERVICE] Exception type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return jsonify({'message': f'Error updating document: {str(e)}'}), 500

@app.route('/documents/<int:document_id>', methods=['DELETE'])
@require_auth
@require_role(['librarian', 'manager'])
def delete_document(document_id):
    print(f"[DOCUMENT SERVICE] Starting deletion for document ID: {document_id}")

    document = Document.query.get(document_id)
    if not document:
        print(f"[DOCUMENT SERVICE] Document with ID {document_id} not found")
        return jsonify({'message': 'Document not found'}), 404

    print(f"[DOCUMENT SERVICE] Found document: {document.title} (Type: {document.document_type})")

    try:
        # Remove associated image if exists
        if document.image_path:
            image_full_path = os.path.join(app.config['UPLOAD_FOLDER'], document.image_path)
            if os.path.exists(image_full_path):
                print(f"[DOCUMENT SERVICE] Removing image file: {image_full_path}")
                os.remove(image_full_path)
            else:
                print(f"[DOCUMENT SERVICE] Image file not found: {image_full_path}")

        # Handle polymorphic deletion properly
        # With proper foreign key constraints, we need to delete child records first
        document_type = document.document_type
        document_title = document.title

        print(f"[DOCUMENT SERVICE] Handling deletion for document type: {document_type}")

        # Step 1: Delete child records first to respect foreign key constraints
        if document_type == 'book':
            # Delete from books table first
            print(f"[DOCUMENT SERVICE] Deleting book record first")
            db.session.execute(text("DELETE FROM books WHERE id = :id"), {"id": document_id})
            print(f"[DOCUMENT SERVICE] Book record deleted")
        elif document_type == 'periodical':
            # Delete from periodicals table first
            print(f"[DOCUMENT SERVICE] Deleting periodical record first")
            db.session.execute(text("DELETE FROM periodicals WHERE id = :id"), {"id": document_id})
            print(f"[DOCUMENT SERVICE] Periodical record deleted")

        # Step 2: Now delete the parent document record
        print(f"[DOCUMENT SERVICE] Deleting parent document record")
        db.session.delete(document)

        # Step 3: Commit all changes
        print(f"[DOCUMENT SERVICE] Committing deletion to database")
        db.session.commit()
        print(f"[DOCUMENT SERVICE] Deletion committed successfully")

        print(f"[DOCUMENT SERVICE] Successfully deleted document: {document_title} (Type: {document_type})")
        return jsonify({'message': 'Document deleted successfully'}), 200

    except Exception as e:
        db.session.rollback()
        print(f"[DOCUMENT SERVICE] Error deleting document: {str(e)}")
        print(f"[DOCUMENT SERVICE] Exception type: {type(e).__name__}")

        # Provide more specific error messages
        error_message = str(e)
        if "foreign key constraint" in error_message.lower():
            return jsonify({
                'message': 'Cannot delete document due to related records. Please contact administrator.',
                'error_type': 'foreign_key_constraint',
                'details': str(e)
            }), 409
        else:
            return jsonify({'message': f'Error deleting document: {str(e)}'}), 500

@app.route('/generate-barcode', methods=['GET'])
@require_auth
@require_role(['librarian', 'manager'])
def generate_barcode():
    while True:
        barcode = str(uuid.uuid4())[:12].upper()
        if not Document.query.filter_by(barcode=barcode).first():
            return jsonify({'barcode': barcode}), 200

@app.route('/categories', methods=['GET'])
@require_auth
def get_categories():
    categories = db.session.query(Document.category).distinct().filter(Document.category.isnot(None)).all()
    return jsonify([cat[0] for cat in categories if cat[0]]), 200

@app.route('/subjects', methods=['GET'])
@require_auth
def get_subjects():
    subjects = db.session.query(Document.subject).distinct().filter(Document.subject.isnot(None)).all()
    return jsonify([subj[0] for subj in subjects if subj[0]]), 200

@app.route('/test-documents', methods=['GET'])
def test_documents():
    """Test documents endpoint without authentication for debugging"""
    search = request.args.get('search', '')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)

    print(f"[DOCUMENT SERVICE] Test documents request - search: '{search}'")

    query = Document.query

    if search:
        search_term = f"%{search}%"
        print(f"[DOCUMENT SERVICE] Applying search filter: '{search_term}'")

        query = query.outerjoin(Book, Document.id == Book.id)
        query = query.filter(
            db.or_(
                Document.title.ilike(search_term),
                Document.description.ilike(search_term),
                Document.keywords.ilike(search_term),
                Document.publisher.ilike(search_term),
                Book.author.ilike(search_term)
            )
        )

    documents = query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

    print(f"[DOCUMENT SERVICE] Found {documents.total} documents")

    return jsonify({
        'documents': [doc.to_dict() for doc in documents.items],
        'total': documents.total,
        'pages': documents.pages,
        'current_page': page,
        'per_page': per_page,
        'search': search
    }), 200

@app.route('/debug/search', methods=['GET'])
def debug_search():
    """Debug route to test search functionality"""
    search_term = request.args.get('q', '')

    if not search_term:
        return jsonify({
            'error': 'Please provide a search term using ?q=your_search_term',
            'available_documents': [
                'Introduction to Python Programming',
                'Data Science with Python',
                'Web Development Fundamentals'
            ],
            'available_authors': ['John Smith', 'Jane Doe', 'Mike Johnson'],
            'try_searches': [
                '/debug/search?q=python',
                '/debug/search?q=john',
                '/debug/search?q=data',
                '/debug/search?q=web'
            ]
        }), 400

    print(f"[DEBUG SEARCH] Testing search for: '{search_term}'")

    # Test the same logic as the main search
    search_pattern = f"%{search_term}%"
    query = Document.query.outerjoin(Book, Document.id == Book.id)

    # Get all documents first
    all_docs = Document.query.all()
    print(f"[DEBUG SEARCH] Total documents in DB: {len(all_docs)}")

    for doc in all_docs:
        book = Book.query.get(doc.id) if doc.document_type == 'book' else None
        author = book.author if book else 'N/A'
        print(f"[DEBUG SEARCH] Doc {doc.id}: '{doc.title}' by {author}")
        print(f"  - Description: {doc.description}")
        print(f"  - Keywords: {doc.keywords}")
        print(f"  - Publisher: {doc.publisher}")

    # Now test the search
    results = query.filter(
        db.or_(
            Document.title.ilike(search_pattern),
            Document.description.ilike(search_pattern),
            Document.keywords.ilike(search_pattern),
            Document.publisher.ilike(search_pattern),
            Book.author.ilike(search_pattern)
        )
    ).all()

    print(f"[DEBUG SEARCH] Search results: {len(results)} documents found")

    result_data = []
    for doc in results:
        book = Book.query.get(doc.id) if doc.document_type == 'book' else None
        result_data.append({
            'id': doc.id,
            'title': doc.title,
            'author': book.author if book else None,
            'description': doc.description,
            'keywords': doc.keywords,
            'publisher': doc.publisher,
            'type': doc.document_type
        })

    return jsonify({
        'search_term': search_term,
        'search_pattern': search_pattern,
        'total_documents': len(all_docs),
        'results_found': len(results),
        'results': result_data
    }), 200

# Initialize database with demo documents
def init_demo_documents():
    """Initialize demo documents for testing"""
    print(f"[INIT] Checking if demo documents need to be created...")

    existing_count = Document.query.count()
    print(f"[INIT] Found {existing_count} existing documents")

    if existing_count == 0:
        print(f"[INIT] Creating demo documents...")
        demo_books = [
            {
                'title': 'Introduction to Python Programming',
                'description': 'A comprehensive guide to Python programming for beginners',
                'document_type': 'book',
                'barcode': 'BOOK001',
                'author': 'John Smith',
                'category': 'Technology',
                'subject': 'Programming',
                'keywords': 'python, programming, coding',
                'publisher': 'Tech Books Publishing',
                'language': 'English',
                'pages': 350,
                'format': 'paperback',
                'total_copies': 3,
                'available_copies': 3,
                'created_by': 1
            },
            {
                'title': 'Data Science with Python',
                'description': 'Learn data science using Python libraries',
                'document_type': 'book',
                'barcode': 'BOOK002',
                'author': 'Jane Doe',
                'category': 'Technology',
                'subject': 'Data Science',
                'keywords': 'data science, python, analytics',
                'publisher': 'Data Publishing',
                'language': 'English',
                'pages': 420,
                'format': 'hardcover',
                'total_copies': 2,
                'available_copies': 2,
                'created_by': 1
            },
            {
                'title': 'Web Development Fundamentals',
                'description': 'Complete guide to modern web development',
                'document_type': 'book',
                'barcode': 'BOOK003',
                'author': 'Mike Johnson',
                'category': 'Technology',
                'subject': 'Web Development',
                'keywords': 'web, html, css, javascript',
                'publisher': 'Web Masters Press',
                'language': 'English',
                'pages': 280,
                'format': 'paperback',
                'total_copies': 5,
                'available_copies': 4,
                'created_by': 1
            }
        ]

        for book_data in demo_books:
            book = Book(**book_data)
            db.session.add(book)

        db.session.commit()
        print("Demo documents created successfully!")

# Initialize database
with app.app_context():
    db.create_all()
    init_demo_documents()

from flask import send_from_directory

# Ensure the upload folder exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

@app.route('/uploads/<path:filename>')
def uploaded_file(filename):
    # Normalize the path to handle different separators
    normalized_filename = os.path.normpath(filename)
    full_path = os.path.join(app.config['UPLOAD_FOLDER'], normalized_filename)

    print(f"[UPLOAD DEBUG] Requested file: {filename}")
    print(f"[UPLOAD DEBUG] Normalized: {normalized_filename}")
    print(f"[UPLOAD DEBUG] Full path: {full_path}")
    print(f"[UPLOAD DEBUG] File exists: {os.path.exists(full_path)}")

    if os.path.exists(full_path):
        return send_from_directory(app.config['UPLOAD_FOLDER'], normalized_filename)
    else:
        # List available files for debugging
        available_files = os.listdir(app.config['UPLOAD_FOLDER']) if os.path.exists(app.config['UPLOAD_FOLDER']) else []
        print(f"[UPLOAD DEBUG] Available files: {available_files}")
        return jsonify({
            'error': 'File not found',
            'requested': filename,
            'available_files': available_files
        }), 404

@app.route('/debug/documents', methods=['GET'])
def debug_documents():
    """Debug route to check documents and their images"""
    try:
        documents = Document.query.all()
        result = []

        print(f"[DEBUG] Found {len(documents)} documents in database:")

        for doc in documents:
            # Get author if it's a book
            author = None
            if doc.document_type == 'book':
                book = Book.query.get(doc.id)
                if book:
                    author = book.author

            doc_info = {
                'id': doc.id,
                'title': doc.title,
                'author': author,
                'type': doc.document_type,
                'category': doc.category,
                'subject': doc.subject,
                'keywords': doc.keywords,
                'publisher': doc.publisher,
                'description': doc.description,
                'available_copies': doc.available_copies,
                'total_copies': doc.total_copies,
                'image_path': doc.image_path,
                'image_exists': False
            }

            print(f"[DEBUG] Document {doc.id}: '{doc.title}' by {author} (Type: {doc.document_type}, Category: {doc.category})")

            if doc.image_path:
                full_path = os.path.join(app.config['UPLOAD_FOLDER'], doc.image_path)
                doc_info['image_exists'] = os.path.exists(full_path)
                doc_info['full_image_path'] = full_path

            result.append(doc_info)

        # Also list all files in uploads folder
        upload_files = []
        if os.path.exists(app.config['UPLOAD_FOLDER']):
            upload_files = os.listdir(app.config['UPLOAD_FOLDER'])

        return jsonify({
            'documents': result,
            'upload_folder': app.config['UPLOAD_FOLDER'],
            'upload_files': upload_files
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/statistics', methods=['GET'])
@require_auth
def get_statistics():
    """Get comprehensive library statistics"""
    try:
        print("[DOCUMENT SERVICE] Generating statistics...")

        # Basic counts
        total_documents = Document.query.count()
        available_documents = Document.query.filter(Document.available_copies > 0).count()
        unavailable_documents = total_documents - available_documents

        # Document type statistics
        type_stats = db.session.query(
            Document.document_type,
            db.func.count(Document.id).label('count')
        ).group_by(Document.document_type).all()

        # Category statistics
        category_stats = db.session.query(
            Document.category,
            db.func.count(Document.id).label('count')
        ).filter(Document.category.isnot(None)).group_by(Document.category).all()

        # Subject statistics
        subject_stats = db.session.query(
            Document.subject,
            db.func.count(Document.id).label('count')
        ).filter(Document.subject.isnot(None)).group_by(Document.subject).all()

        # Language statistics
        language_stats = db.session.query(
            Document.language,
            db.func.count(Document.id).label('count')
        ).filter(Document.language.isnot(None)).group_by(Document.language).all()

        # Publisher statistics (top 10)
        publisher_stats = db.session.query(
            Document.publisher,
            db.func.count(Document.id).label('count')
        ).filter(Document.publisher.isnot(None)).group_by(Document.publisher).order_by(db.func.count(Document.id).desc()).limit(10).all()

        # Recent additions (last 30 days)
        from datetime import datetime, timedelta
        thirty_days_ago = datetime.now() - timedelta(days=30)
        recent_additions = Document.query.filter(Document.created_at >= thirty_days_ago).count()

        # Documents with images
        documents_with_images = Document.query.filter(Document.image_path.isnot(None)).count()

        # Copy statistics
        total_copies = db.session.query(db.func.sum(Document.total_copies)).scalar() or 0
        available_copies = db.session.query(db.func.sum(Document.available_copies)).scalar() or 0

        # Most popular category
        most_popular_category = None
        most_popular_count = 0
        if category_stats:
            most_popular = max(category_stats, key=lambda x: x[1])
            most_popular_category = most_popular[0]
            most_popular_count = most_popular[1]

        # Availability percentage
        availability_percentage = round((available_documents / total_documents * 100) if total_documents > 0 else 0, 1)

        statistics = {
            'overview': {
                'total_documents': total_documents,
                'available_documents': available_documents,
                'unavailable_documents': unavailable_documents,
                'total_copies': total_copies,
                'available_copies': available_copies,
                'availability_percentage': availability_percentage,
                'recent_additions': recent_additions,
                'documents_with_images': documents_with_images
            },
            'by_type': {stat[0]: stat[1] for stat in type_stats},
            'by_category': {stat[0]: stat[1] for stat in category_stats},
            'by_subject': {stat[0]: stat[1] for stat in subject_stats},
            'by_language': {stat[0]: stat[1] for stat in language_stats},
            'top_publishers': [{'name': stat[0], 'count': stat[1]} for stat in publisher_stats],
            'most_popular_category': {
                'name': most_popular_category,
                'count': most_popular_count
            }
        }

        print(f"[DOCUMENT SERVICE] Statistics generated: {total_documents} total documents")
        return jsonify(statistics), 200

    except Exception as e:
        print(f"[DOCUMENT SERVICE] Error generating statistics: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/documents/<int:document_id>/availability', methods=['PUT'])
def update_document_availability(document_id):
    """Update document availability (for member service)"""
    try:
        data = request.get_json()
        action = data.get('action')  # 'borrow' or 'return'

        if action not in ['borrow', 'return']:
            return jsonify({'message': 'Invalid action. Must be "borrow" or "return"'}), 400

        document = Document.query.get(document_id)
        if not document:
            return jsonify({'message': 'Document not found'}), 404

        if action == 'borrow':
            if document.available_copies <= 0:
                return jsonify({'message': 'No copies available for borrowing'}), 400
            document.available_copies -= 1
        elif action == 'return':
            if document.available_copies >= document.total_copies:
                return jsonify({'message': 'All copies are already available'}), 400
            document.available_copies += 1

        document.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'message': f'Document availability updated successfully',
            'document_id': document_id,
            'action': action,
            'available_copies': document.available_copies,
            'total_copies': document.total_copies
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error updating availability: {str(e)}'}), 500

@app.route('/test-db', methods=['GET'])
def test_db():
    """Test database connection and operations"""
    try:
        # Test database connection
        print("[TEST] Testing database connection...")
        db_version = db.session.execute(text("SELECT version()")).scalar()
        print(f"[TEST] Database version: {db_version}")
        
        # Test document query
        print("[TEST] Testing document query...")
        documents = Document.query.limit(5).all()
        print(f"[TEST] Found {len(documents)} documents")
        
        # Print document details
        for doc in documents:
            print(f"[TEST] Document ID: {doc.id}, Title: {doc.title}, Type: {doc.document_type}")
        
        return jsonify({
            'status': 'success',
            'message': 'Database connection test successful',
            'db_version': db_version,
            'document_count': len(documents),
            'documents': [{'id': doc.id, 'title': doc.title, 'type': doc.document_type} for doc in documents]
        }), 200
    except Exception as e:
        print(f"[TEST] Database test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'status': 'error',
            'message': f'Database test failed: {str(e)}'
        }), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
