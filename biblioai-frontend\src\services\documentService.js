import axios from 'axios';

const API_URL = 'http://localhost:5000/api';

// Create axios instance with auth header
const createAuthHeader = () => {
  const token = localStorage.getItem('token');
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// Get all documents with optional filters
export const getDocuments = async (params = {}) => {
  try {
    const response = await axios.get(`${API_URL}/documents`, {
      headers: createAuthHeader(),
      params
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Get a specific document by ID
export const getDocument = async (documentId) => {
  try {
    const response = await axios.get(`${API_URL}/documents/${documentId}`, {
      headers: createAuthHeader()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Create a new document
export const createDocument = async (documentData) => {
  try {
    const formData = new FormData();
    
    // Append all fields
    Object.keys(documentData).forEach(key => {
      if (key === 'image' && documentData[key]) {
        formData.append('image', documentData[key]);
      } else if (documentData[key] !== null && documentData[key] !== undefined) {
        formData.append(key, documentData[key]);
      }
    });
    
    const response = await axios.post(`${API_URL}/documents`, formData, {
      headers: {
        ...createAuthHeader(),
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Update a document
export const updateDocument = async (documentId, documentData) => {
  try {
    if (!documentId) {
      console.error('UPDATE ERROR: Missing document ID');
      throw new Error('Document ID is required for updates');
    }
    
    console.log('UPDATE DEBUG: Starting update for document ID:', documentId);
    
    // Check if we have an image file
    const hasImageFile = documentData.image && documentData.image instanceof File;
    
    let response;
    
    if (hasImageFile) {
      // Use FormData for requests with files
      const formData = new FormData();
      
      // Add all fields to the form data
      Object.keys(documentData).forEach(key => {
        // Skip the ID field as it's in the URL
        if (key === 'id') return;
        
        // Skip image_path field to avoid conflicts
        if (key === 'image_path') return;
        
        // Handle image file specially
        if (key === 'image' && documentData[key]) {
          console.log('UPDATE DEBUG: Adding image file to form data:', documentData[key].name);
          formData.append('image', documentData[key]);
        } 
        // Add all other non-null fields
        else if (documentData[key] !== null && documentData[key] !== undefined) {
          console.log(`UPDATE DEBUG: Adding field ${key}=${documentData[key]} to form data`);
          formData.append(key, documentData[key]);
        }
      });
      
      // Log all form data entries for debugging
      console.log('UPDATE DEBUG: Form data entries:');
      for (let pair of formData.entries()) {
        console.log(`  ${pair[0]}: ${pair[1]}`);
      }
      
      // Make the API request with multipart/form-data
      console.log(`UPDATE DEBUG: Sending PUT request with form data to ${API_URL}/documents/${documentId}`);
      response = await axios.put(`${API_URL}/documents/${documentId}`, formData, {
        headers: {
          ...createAuthHeader(),
          'Content-Type': 'multipart/form-data'
        }
      });
    } else {
      // For requests without files, use JSON
      // Create a clean data object without image-related fields
      const jsonData = { ...documentData };
      
      // Remove image and image_path fields
      delete jsonData.image;
      delete jsonData.image_path;
      delete jsonData.id; // Remove ID as it's in the URL
      
      // Log the JSON data
      console.log('UPDATE DEBUG: JSON data for update:', jsonData);
      
      // Make the API request with application/json
      console.log(`UPDATE DEBUG: Sending PUT request with JSON to ${API_URL}/documents/${documentId}`);
      response = await axios.put(`${API_URL}/documents/${documentId}`, jsonData, {
        headers: {
          ...createAuthHeader(),
          'Content-Type': 'application/json'
        }
      });
    }
    
    return response.data;
  } catch (error) {
    console.error('UPDATE ERROR: Failed to update document:', error);
    
    // Log detailed error information
    if (error.response) {
      console.error('UPDATE ERROR: Response status:', error.response.status);
      console.error('UPDATE ERROR: Response data:', error.response.data);
      throw error.response.data || { message: `Server returned status ${error.response.status}` };
    } else if (error.request) {
      console.error('UPDATE ERROR: No response received');
      throw { message: 'No response received from server' };
    } else {
      console.error('UPDATE ERROR: Error message:', error.message);
      throw { message: error.message || 'Unknown error occurred' };
    }
  }
};

// Delete a document
export const deleteDocument = async (documentId) => {
  try {
    const response = await axios.delete(`${API_URL}/documents/${documentId}`, {
      headers: createAuthHeader()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Search documents
export const searchDocuments = async (searchParams) => {
  try {
    const response = await axios.get(`${API_URL}/documents/search`, {
      headers: createAuthHeader(),
      params: searchParams
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Get all categories
export const getCategories = async () => {
  try {
    const response = await axios.get(`${API_URL}/documents/categories`, {
      headers: createAuthHeader()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Get all subjects
export const getSubjects = async () => {
  try {
    const response = await axios.get(`${API_URL}/documents/subjects`, {
      headers: createAuthHeader()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Generate a unique barcode
export const generateBarcode = async () => {
  try {
    const response = await axios.get(`${API_URL}/documents/generate-barcode`, {
      headers: createAuthHeader()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Document type options
export const DOCUMENT_TYPES = [
  { value: 'book', label: 'Book' },
  { value: 'periodical', label: 'Periodical' },
  { value: 'video', label: 'Video' }
];

// Format options
export const FORMAT_OPTIONS = [
  { value: 'hardcover', label: 'Hardcover' },
  { value: 'paperback', label: 'Paperback' },
  { value: 'digital', label: 'Digital' },
  { value: 'audiobook', label: 'Audiobook' },
  { value: 'dvd', label: 'DVD' },
  { value: 'blu-ray', label: 'Blu-ray' },
  { value: 'cd', label: 'CD' }
];

// Language options
export const LANGUAGE_OPTIONS = [
  { value: 'English', label: 'English' },
  { value: 'French', label: 'French' },
  { value: 'Spanish', label: 'Spanish' },
  { value: 'German', label: 'German' },
  { value: 'Italian', label: 'Italian' },
  { value: 'Portuguese', label: 'Portuguese' },
  { value: 'Chinese', label: 'Chinese' },
  { value: 'Japanese', label: 'Japanese' },
  { value: 'Arabic', label: 'Arabic' },
  { value: 'Other', label: 'Other' }
];

// Common categories
export const COMMON_CATEGORIES = [
  'Fiction',
  'Non-Fiction',
  'Science',
  'Technology',
  'History',
  'Biography',
  'Arts',
  'Literature',
  'Philosophy',
  'Religion',
  'Education',
  'Business',
  'Health',
  'Travel',
  'Children',
  'Reference'
];

// Common subjects
export const COMMON_SUBJECTS = [
  'Computer Science',
  'Mathematics',
  'Physics',
  'Chemistry',
  'Biology',
  'Medicine',
  'Engineering',
  'Psychology',
  'Sociology',
  'Economics',
  'Political Science',
  'Law',
  'Art History',
  'Music',
  'Literature',
  'Linguistics'
];
