from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from flask_jwt_extended import JW<PERSON>anager, decode_token
from datetime import datetime, timedelta
import os
import requests

app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'notification-service-secret')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get(
    'DATABASE_URI',
    'mysql+pymysql://root:@localhost:3306/biblioai_notifications'  # XAMPP MySQL configuration
)
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['AUTH_SERVICE_URL'] = os.environ.get('AUTH_SERVICE_URL', 'http://localhost:5001')
# JWT Configuration - Must match auth service
app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET_KEY', 'biblioai-jwt-secret-key-2024')

# Initialize extensions
db = SQLAlchemy(app)
jwt = JWTManager(app)
CORS(app)

# Notification Models
class Notification(db.Model):
    __tablename__ = 'notifications'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False)
    type = db.Column(db.String(50), nullable=False)  # reservation_created, borrowing_created, due_reminder, overdue_notice, etc.
    title = db.Column(db.String(255), nullable=False)
    message = db.Column(db.Text, nullable=False)
    document_id = db.Column(db.Integer)
    is_read = db.Column(db.Boolean, default=False)
    priority = db.Column(db.String(20), default='normal')  # low, normal, high, urgent
    scheduled_for = db.Column(db.DateTime)  # For scheduled notifications
    sent_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'type': self.type,
            'title': self.title,
            'message': self.message,
            'document_id': self.document_id,
            'is_read': self.is_read,
            'priority': self.priority,
            'scheduled_for': self.scheduled_for.isoformat() if self.scheduled_for else None,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class NotificationTemplate(db.Model):
    __tablename__ = 'notification_templates'

    id = db.Column(db.Integer, primary_key=True)
    type = db.Column(db.String(50), unique=True, nullable=False)
    title_template = db.Column(db.String(255), nullable=False)
    message_template = db.Column(db.Text, nullable=False)
    priority = db.Column(db.String(20), default='normal')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'type': self.type,
            'title_template': self.title_template,
            'message_template': self.message_template,
            'priority': self.priority,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

# Authentication middleware
def verify_token():
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None
    
    token = auth_header.split(' ')[1]
    try:
        decoded_token = decode_token(token)
        return decoded_token
    except Exception as e:
        print(f"Token verification failed: {str(e)}")
        return None

def require_auth(f):
    def decorated_function(*args, **kwargs):
        token_data = verify_token()
        if not token_data:
            return jsonify({'message': 'Authentication required'}), 401
        
        request.user_id = int(token_data['sub'])
        request.user_role = token_data.get('role', 'member')
        return f(*args, **kwargs)
    
    decorated_function.__name__ = f.__name__
    return decorated_function

# Helper functions
def get_notification_template(notification_type):
    """Get notification template by type"""
    template = NotificationTemplate.query.filter_by(type=notification_type, is_active=True).first()
    return template

def format_notification_message(template, **kwargs):
    """Format notification message using template and variables"""
    if not template:
        return "Notification", "You have a new notification."
    
    try:
        title = template.title_template.format(**kwargs)
        message = template.message_template.format(**kwargs)
        return title, message
    except Exception as e:
        print(f"Error formatting notification: {str(e)}")
        return template.title_template, template.message_template

# Health check endpoint
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'service': 'notification-service'}), 200

# Notification endpoints
@app.route('/notifications', methods=['GET'])
@require_auth
def get_user_notifications():
    """Get user's notifications"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        unread_only = request.args.get('unread_only', 'false').lower() == 'true'
        
        query = Notification.query.filter_by(user_id=request.user_id)
        
        if unread_only:
            query = query.filter_by(is_read=False)
        
        notifications_pagination = query.order_by(Notification.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        notifications = [notification.to_dict() for notification in notifications_pagination.items]
        
        return jsonify({
            'notifications': notifications,
            'total': notifications_pagination.total,
            'pages': notifications_pagination.pages,
            'current_page': page,
            'per_page': per_page,
            'unread_count': Notification.query.filter_by(user_id=request.user_id, is_read=False).count()
        }), 200
    except Exception as e:
        return jsonify({'message': f'Error fetching notifications: {str(e)}'}), 500

@app.route('/notifications', methods=['POST'])
def create_notification():
    """Create a new notification (internal service call)"""
    try:
        data = request.get_json()
        
        user_id = data.get('user_id')
        notification_type = data.get('type')
        document_id = data.get('document_id')
        custom_message = data.get('message')
        custom_title = data.get('title')
        priority = data.get('priority', 'normal')
        
        if not user_id or not notification_type:
            return jsonify({'message': 'User ID and notification type are required'}), 400
        
        # Get template for this notification type
        template = get_notification_template(notification_type)
        
        # Use custom message/title or format from template
        if custom_title and custom_message:
            title = custom_title
            message = custom_message
        elif template:
            # Format message with provided data
            format_data = {
                'document_title': data.get('document_title', 'Unknown Document'),
                'due_date': data.get('due_date', ''),
                'user_name': data.get('user_name', ''),
                'days_overdue': data.get('days_overdue', 0),
                'fine_amount': data.get('fine_amount', 0)
            }
            title, message = format_notification_message(template, **format_data)
            priority = template.priority
        else:
            title = custom_title or "BiblioAI Notification"
            message = custom_message or "You have a new notification."
        
        # Create notification
        notification = Notification(
            user_id=user_id,
            type=notification_type,
            title=title,
            message=message,
            document_id=document_id,
            priority=priority,
            sent_at=datetime.utcnow()
        )
        
        db.session.add(notification)
        db.session.commit()
        
        return jsonify({
            'message': 'Notification created successfully',
            'notification': notification.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error creating notification: {str(e)}'}), 500

@app.route('/notifications/<int:notification_id>/read', methods=['PUT'])
@require_auth
def mark_notification_read(notification_id):
    """Mark a notification as read"""
    try:
        notification = Notification.query.filter_by(
            id=notification_id,
            user_id=request.user_id
        ).first()
        
        if not notification:
            return jsonify({'message': 'Notification not found'}), 404
        
        notification.is_read = True
        notification.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({'message': 'Notification marked as read'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error marking notification as read: {str(e)}'}), 500

@app.route('/notifications/mark-all-read', methods=['PUT'])
@require_auth
def mark_all_notifications_read():
    """Mark all user notifications as read"""
    try:
        Notification.query.filter_by(
            user_id=request.user_id,
            is_read=False
        ).update({'is_read': True, 'updated_at': datetime.utcnow()})
        
        db.session.commit()
        
        return jsonify({'message': 'All notifications marked as read'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error marking all notifications as read: {str(e)}'}), 500

@app.route('/notifications/<int:notification_id>', methods=['DELETE'])
@require_auth
def delete_notification(notification_id):
    """Delete a notification"""
    try:
        notification = Notification.query.filter_by(
            id=notification_id,
            user_id=request.user_id
        ).first()
        
        if not notification:
            return jsonify({'message': 'Notification not found'}), 404
        
        db.session.delete(notification)
        db.session.commit()
        
        return jsonify({'message': 'Notification deleted successfully'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error deleting notification: {str(e)}'}), 500

# Initialize default notification templates
def init_notification_templates():
    """Initialize default notification templates"""
    if NotificationTemplate.query.count() == 0:
        templates = [
            {
                'type': 'reservation_created',
                'title_template': 'Reservation Confirmed',
                'message_template': 'You have successfully reserved "{document_title}". You will be notified when it becomes available.',
                'priority': 'normal'
            },
            {
                'type': 'borrowing_created',
                'title_template': 'Book Borrowed',
                'message_template': 'You have successfully borrowed "{document_title}". Please return it by {due_date}.',
                'priority': 'normal'
            },
            {
                'type': 'document_returned',
                'title_template': 'Book Returned',
                'message_template': 'You have successfully returned "{document_title}". Thank you!',
                'priority': 'normal'
            },
            {
                'type': 'borrowing_renewed',
                'title_template': 'Book Renewed',
                'message_template': 'You have successfully renewed "{document_title}". New due date: {due_date}.',
                'priority': 'normal'
            },
            {
                'type': 'due_reminder',
                'title_template': 'Due Date Reminder',
                'message_template': 'Reminder: "{document_title}" is due on {due_date}. Please return it on time to avoid fines.',
                'priority': 'high'
            },
            {
                'type': 'overdue_notice',
                'title_template': 'Overdue Notice',
                'message_template': 'OVERDUE: "{document_title}" was due on {due_date}. Please return it immediately. Fine: ${fine_amount}',
                'priority': 'urgent'
            }
        ]
        
        for template_data in templates:
            template = NotificationTemplate(**template_data)
            db.session.add(template)
        
        db.session.commit()
        print("Default notification templates created successfully!")

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        init_notification_templates()
        print("Notification Service database tables created successfully!")
    
    app.run(debug=True, host='0.0.0.0', port=5007)
