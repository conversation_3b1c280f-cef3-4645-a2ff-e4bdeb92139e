import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
// Simple icon components
const Loader2 = ({ className }) => <div className={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${className}`}></div>;
const BookOpen = ({ className }) => <span className={className}>📖</span>;
const TrendingUp = ({ className }) => <span className={className}>📈</span>;
const Sparkles = ({ className }) => <span className={className}>✨</span>;
import { useAuth } from '../../contexts/AuthContext';

const AIRecommendations = () => {
  const [recommendations, setRecommendations] = useState([]);
  const [trending, setTrending] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('recommendations');
  const { token } = useAuth();

  useEffect(() => {
    fetchRecommendations();
    fetchTrending();
  }, []);

  const fetchRecommendations = async () => {
    try {
      const response = await fetch('/api/members/ai/recommendations', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setRecommendations(data.recommendations || []);
      }
    } catch (error) {
      console.error('Error fetching recommendations:', error);
    }
  };

  const fetchTrending = async () => {
    try {
      const response = await fetch('/api/members/ai/trending', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTrending(data.trending_books || []);
      }
    } catch (error) {
      console.error('Error fetching trending books:', error);
    } finally {
      setLoading(false);
    }
  };

  const reserveBook = async (documentId) => {
    try {
      const response = await fetch('/api/members/reservations', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ document_id: documentId })
      });

      if (response.ok) {
        alert('Book reserved successfully!');
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to reserve book');
      }
    } catch (error) {
      console.error('Error reserving book:', error);
      alert('Failed to reserve book');
    }
  };

  const BookCard = ({ book, reasons = [], score = 0, borrowCount = 0 }) => (
    <Card className="mb-4 hover:shadow-lg transition-shadow">
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-semibold text-lg">{book.title}</h3>
          {score > 0 && (
            <Badge variant="secondary" className="ml-2">
              <Sparkles className="w-3 h-3 mr-1" />
              {score} match
            </Badge>
          )}
          {borrowCount > 0 && (
            <Badge variant="outline" className="ml-2">
              <TrendingUp className="w-3 h-3 mr-1" />
              {borrowCount} borrows
            </Badge>
          )}
        </div>
        
        <p className="text-sm text-gray-600 mb-2">
          by {book.author} • {book.category}
        </p>
        
        {book.description && (
          <p className="text-sm text-gray-700 mb-3 line-clamp-2">
            {book.description}
          </p>
        )}
        
        {reasons && reasons.length > 0 && (
          <div className="mb-3">
            <p className="text-xs font-medium text-blue-600 mb-1">Why recommended:</p>
            <ul className="text-xs text-gray-600">
              {reasons.map((reason, index) => (
                <li key={index} className="mb-1">• {reason}</li>
              ))}
            </ul>
          </div>
        )}
        
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            Available: {book.available_copies}/{book.total_copies}
          </div>
          <Button 
            size="sm" 
            onClick={() => reserveBook(book.id)}
            disabled={book.available_copies === 0}
          >
            <BookOpen className="w-4 h-4 mr-1" />
            {book.available_copies > 0 ? 'Reserve' : 'Unavailable'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="w-8 h-8 animate-spin" />
        <span className="ml-2">Loading AI recommendations...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Sparkles className="w-5 h-5 mr-2 text-purple-500" />
            AI-Powered Book Discovery
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4 mb-6">
            <Button
              variant={activeTab === 'recommendations' ? 'default' : 'outline'}
              onClick={() => setActiveTab('recommendations')}
              className="flex items-center"
            >
              <Sparkles className="w-4 h-4 mr-1" />
              For You ({recommendations.length})
            </Button>
            <Button
              variant={activeTab === 'trending' ? 'default' : 'outline'}
              onClick={() => setActiveTab('trending')}
              className="flex items-center"
            >
              <TrendingUp className="w-4 h-4 mr-1" />
              Trending ({trending.length})
            </Button>
          </div>

          {activeTab === 'recommendations' && (
            <div>
              {recommendations.length > 0 ? (
                <div>
                  <p className="text-sm text-gray-600 mb-4">
                    Based on your reading history and preferences
                  </p>
                  {recommendations.map((rec, index) => (
                    <BookCard
                      key={index}
                      book={rec.book}
                      reasons={rec.reasons}
                      score={rec.score}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <BookOpen className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">
                    Start borrowing books to get personalized recommendations!
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'trending' && (
            <div>
              {trending.length > 0 ? (
                <div>
                  <p className="text-sm text-gray-600 mb-4">
                    Most popular books in the last 30 days
                  </p>
                  {trending.map((book, index) => (
                    <BookCard
                      key={index}
                      book={book}
                      borrowCount={book.borrow_count}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <TrendingUp className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">
                    No trending data available yet.
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AIRecommendations;
