import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';

const LoadingSpinner = ({ message = 'Loading...', size = 'default' }) => {
  const { getCurrentColors } = useTheme();
  const colors = getCurrentColors();

  const sizeClasses = {
    small: 'h-6 w-6',
    default: 'h-12 w-12',
    large: 'h-16 w-16'
  };

  const textSizes = {
    small: 'text-sm',
    default: 'text-lg',
    large: 'text-xl'
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="flex flex-col items-center space-y-4">
        <div 
          className={`animate-spin rounded-full border-2 border-t-transparent ${sizeClasses[size]}`}
          style={{ 
            borderColor: colors.border,
            borderTopColor: colors.primary 
          }}
        ></div>
        <span 
          className={`${textSizes[size]} font-medium`}
          style={{ color: colors.text }}
        >
          {message}
        </span>
      </div>
    </div>
  );
};

export default LoadingSpinner;
