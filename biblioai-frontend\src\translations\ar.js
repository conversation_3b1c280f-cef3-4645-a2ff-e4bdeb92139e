const translations = {
  // مشترك
  common: {
    loading: 'جاري التحميل...',
    error: 'خطأ',
    success: 'نجح',
    cancel: 'إلغاء',
    confirm: 'تأكيد',
    save: 'حفظ',
    edit: 'تعديل',
    delete: 'حذف',
    search: 'بحث',
    filter: 'تصفية',
    sort: 'ترتيب',
    refresh: 'تحديث',
    back: 'رجوع',
    next: 'التالي',
    previous: 'السابق',
    close: 'إغلاق',
    open: 'فتح',
    yes: 'نعم',
    no: 'لا',
    ok: 'موافق',
    apply: 'تطبيق',
    reset: 'إعادة تعيين',
    clear: 'مسح'
  },

  // التنقل
  nav: {
    home: 'الرئيسية',
    dashboard: 'لوحة التحكم',
    books: 'الكتب',
    members: 'الأعضاء',
    reports: 'التقارير',
    settings: 'الإعدادات',
    profile: 'الملف الشخصي',
    logout: 'تسجيل الخروج',
    login: 'تسجيل الدخول',
    register: 'التسجيل'
  },

  // المصادقة
  auth: {
    login: 'تسجيل الدخول',
    register: 'التسجيل',
    logout: 'تسجيل الخروج',
    username: 'اسم المستخدم',
    password: 'كلمة المرور',
    email: 'البريد الإلكتروني',
    confirmPassword: 'تأكيد كلمة المرور',
    forgotPassword: 'نسيت كلمة المرور؟',
    rememberMe: 'تذكرني',
    loginSuccess: 'تم تسجيل الدخول بنجاح',
    loginError: 'بيانات اعتماد غير صحيحة',
    registerSuccess: 'تم التسجيل بنجاح',
    registerError: 'فشل في التسجيل',
    logoutSuccess: 'تم تسجيل الخروج بنجاح',
    invalidCredentials: 'اسم المستخدم أو كلمة المرور غير صحيحة',
    accountCreated: 'تم إنشاء الحساب بنجاح',
    passwordMismatch: 'كلمات المرور غير متطابقة',
    emailRequired: 'البريد الإلكتروني مطلوب',
    usernameRequired: 'اسم المستخدم مطلوب',
    passwordRequired: 'كلمة المرور مطلوبة'
  },

  // لوحة التحكم
  dashboard: {
    title: 'لوحة التحكم',
    overview: 'نظرة عامة',
    statistics: 'الإحصائيات',
    recentActivity: 'النشاط الأخير',
    quickActions: 'إجراءات سريعة',
    totalBooks: 'إجمالي الكتب',
    availableBooks: 'الكتب المتاحة',
    borrowedBooks: 'الكتب المستعارة',
    totalMembers: 'إجمالي الأعضاء',
    activeMembers: 'الأعضاء النشطون',
    overdueBooks: 'الكتب المتأخرة',
    reservations: 'الحجوزات',
    notifications: 'الإشعارات'
  },

  // الكتب
  books: {
    title: 'الكتب',
    addBook: 'إضافة كتاب',
    editBook: 'تعديل الكتاب',
    deleteBook: 'حذف الكتاب',
    bookTitle: 'عنوان الكتاب',
    author: 'المؤلف',
    isbn: 'الرقم المعياري',
    category: 'الفئة',
    subject: 'الموضوع',
    publisher: 'الناشر',
    publishedDate: 'تاريخ النشر',
    pages: 'الصفحات',
    language: 'اللغة',
    description: 'الوصف',
    totalCopies: 'إجمالي النسخ',
    availableCopies: 'النسخ المتاحة',
    location: 'الموقع',
    status: 'الحالة',
    available: 'متاح',
    borrowed: 'مستعار',
    reserved: 'محجوز',
    maintenance: 'صيانة',
    searchBooks: 'البحث عن الكتب',
    filterByCategory: 'تصفية حسب الفئة',
    sortBy: 'ترتيب حسب',
    bookAdded: 'تم إضافة الكتاب بنجاح',
    bookUpdated: 'تم تحديث الكتاب بنجاح',
    bookDeleted: 'تم حذف الكتاب بنجاح',
    bookNotFound: 'الكتاب غير موجود',
    noBooks: 'لم يتم العثور على كتب'
  },

  // الأعضاء
  members: {
    title: 'الأعضاء',
    addMember: 'إضافة عضو',
    editMember: 'تعديل العضو',
    deleteMember: 'حذف العضو',
    memberName: 'اسم العضو',
    memberType: 'نوع العضوية',
    studentId: 'رقم الطالب',
    phone: 'الهاتف',
    address: 'العنوان',
    joinDate: 'تاريخ الانضمام',
    status: 'الحالة',
    active: 'نشط',
    inactive: 'غير نشط',
    suspended: 'معلق',
    borrowingHistory: 'تاريخ الاستعارة',
    currentBorrowings: 'الاستعارات الحالية',
    reservations: 'الحجوزات',
    fines: 'الغرامات',
    memberAdded: 'تم إضافة العضو بنجاح',
    memberUpdated: 'تم تحديث العضو بنجاح',
    memberDeleted: 'تم حذف العضو بنجاح',
    memberNotFound: 'العضو غير موجود',
    noMembers: 'لم يتم العثور على أعضاء'
  },

  // الاستعارة
  borrowing: {
    title: 'الاستعارة',
    borrowBook: 'استعارة كتاب',
    returnBook: 'إرجاع الكتاب',
    renewBook: 'تجديد الكتاب',
    borrowDate: 'تاريخ الاستعارة',
    dueDate: 'تاريخ الاستحقاق',
    returnDate: 'تاريخ الإرجاع',
    renewalCount: 'عدد التجديدات',
    fine: 'الغرامة',
    overdue: 'متأخر',
    returned: 'مُرجع',
    borrowed: 'مستعار',
    myBorrowings: 'استعاراتي',
    borrowingHistory: 'تاريخ الاستعارة',
    bookBorrowed: 'تم استعارة الكتاب بنجاح',
    bookReturned: 'تم إرجاع الكتاب بنجاح',
    bookRenewed: 'تم تجديد الكتاب بنجاح',
    borrowingFailed: 'فشل في استعارة الكتاب',
    returnFailed: 'فشل في إرجاع الكتاب',
    renewalFailed: 'فشل في تجديد الكتاب',
    maxRenewalsReached: 'تم الوصول للحد الأقصى من التجديدات',
    bookOverdue: 'الكتاب متأخر',
    noBorrowings: 'لم يتم العثور على استعارات'
  },

  // الحجوزات
  reservations: {
    title: 'الحجوزات',
    reserveBook: 'حجز كتاب',
    cancelReservation: 'إلغاء الحجز',
    reservationDate: 'تاريخ الحجز',
    expiryDate: 'تاريخ انتهاء الصلاحية',
    status: 'الحالة',
    pending: 'في الانتظار',
    ready: 'جاهز',
    expired: 'منتهي الصلاحية',
    cancelled: 'ملغي',
    myReservations: 'حجوزاتي',
    bookReserved: 'تم حجز الكتاب بنجاح',
    reservationCancelled: 'تم إلغاء الحجز بنجاح',
    reservationFailed: 'فشل في حجز الكتاب',
    reservationExpired: 'انتهت صلاحية الحجز',
    noReservations: 'لم يتم العثور على حجوزات'
  },

  // ميزات الذكاء الاصطناعي
  ai: {
    title: 'ميزات الذكاء الاصطناعي',
    recommendations: 'توصيات الذكاء الاصطناعي',
    chatAssistant: 'مساعد الدردشة الذكي',
    smartSearch: 'البحث الذكي',
    trending: 'الكتب الرائجة',
    forYou: 'لك',
    basedOnHistory: 'بناءً على تاريخ قراءتك وتفضيلاتك',
    popularBooks: 'أكثر الكتب شعبية في آخر 30 يوماً',
    whyRecommended: 'لماذا موصى به:',
    matchesCategory: 'يطابق فئتك المفضلة: {{category}}',
    matchesSubject: 'يطابق اهتمامك بـ: {{subject}}',
    favoriteAuthor: 'بواسطة مؤلفك المفضل: {{author}}',
    aiAssistantWelcome: 'مرحباً! أنا مساعد المكتبة الذكي. يمكنني مساعدتك في العثور على الكتب والإجابة على أسئلة حول سياسات المكتبة ومساعدتك في الاستعارات والحجوزات. كيف يمكنني مساعدتك اليوم؟',
    quickActions: 'إجراءات سريعة:',
    findProgrammingBooks: 'العثور على كتب البرمجة',
    checkBorrowings: 'فحص استعاراتي الحالية',
    renewBook: 'كيف أجدد كتاباً؟',
    libraryPolicies: 'ما هي سياسات المكتبة؟',
    recommendBooks: 'اقترح لي كتباً',
    thinking: 'أفكر...',
    typeMessage: 'اسألني أي شيء عن المكتبة...',
    noRecommendations: 'ابدأ باستعارة الكتب للحصول على توصيات شخصية!',
    noTrendingData: 'لا توجد بيانات رائجة متاحة حتى الآن.',
    aiError: 'عذراً، واجهت خطأ. يرجى المحاولة مرة أخرى.',
    connectionError: 'عذراً، أواجه مشاكل في الاتصال الآن. يرجى المحاولة لاحقاً.'
  },

  // الإعدادات
  settings: {
    title: 'الإعدادات',
    general: 'عام',
    appearance: 'المظهر',
    language: 'اللغة',
    notifications: 'الإشعارات',
    privacy: 'الخصوصية',
    security: 'الأمان',
    account: 'الحساب',
    darkMode: 'الوضع المظلم',
    lightMode: 'الوضع المضيء',
    theme: 'السمة',
    changeLanguage: 'تغيير اللغة',
    selectLanguage: 'اختر اللغة',
    emailNotifications: 'إشعارات البريد الإلكتروني',
    pushNotifications: 'الإشعارات المنبثقة',
    smsNotifications: 'إشعارات الرسائل النصية',
    changePassword: 'تغيير كلمة المرور',
    currentPassword: 'كلمة المرور الحالية',
    newPassword: 'كلمة المرور الجديدة',
    confirmNewPassword: 'تأكيد كلمة المرور الجديدة',
    passwordChanged: 'تم تغيير كلمة المرور بنجاح',
    settingsSaved: 'تم حفظ الإعدادات بنجاح'
  },

  // الإشعارات
  notifications: {
    title: 'الإشعارات',
    markAsRead: 'تحديد كمقروء',
    markAllAsRead: 'تحديد الكل كمقروء',
    deleteNotification: 'حذف الإشعار',
    noNotifications: 'لا توجد إشعارات',
    bookDueSoon: 'كتاب مستحق قريباً: {{title}}',
    bookOverdue: 'كتاب متأخر: {{title}}',
    bookReady: 'الكتاب المحجوز جاهز: {{title}}',
    reservationExpiring: 'الحجز ينتهي: {{title}}',
    newBookAvailable: 'كتاب جديد متاح: {{title}}',
    accountSuspended: 'تم تعليق حسابك',
    fineAdded: 'تم إضافة غرامة: {{amount}} ريال',
    reminderDue: 'تذكير: الكتاب مستحق غداً',
    notificationRead: 'تم تحديد الإشعار كمقروء',
    allNotificationsRead: 'تم تحديد جميع الإشعارات كمقروءة'
  },

  // الأخطاء
  errors: {
    general: 'حدث خطأ',
    networkError: 'خطأ في الشبكة. يرجى فحص اتصالك.',
    serverError: 'خطأ في الخادم. يرجى المحاولة لاحقاً.',
    notFound: 'المورد غير موجود',
    unauthorized: 'غير مخول لك تنفيذ هذا الإجراء',
    forbidden: 'الوصول محظور',
    validationError: 'يرجى فحص المدخلات',
    sessionExpired: 'انتهت جلستك. يرجى تسجيل الدخول مرة أخرى.',
    fileUploadError: 'فشل في رفع الملف',
    fileSizeError: 'حجم الملف كبير جداً',
    fileTypeError: 'نوع الملف غير صحيح'
  },

  // رسائل النجاح
  success: {
    operationCompleted: 'تمت العملية بنجاح',
    dataSaved: 'تم حفظ البيانات بنجاح',
    dataUpdated: 'تم تحديث البيانات بنجاح',
    dataDeleted: 'تم حذف البيانات بنجاح',
    emailSent: 'تم إرسال البريد الإلكتروني بنجاح',
    passwordReset: 'تم إعادة تعيين كلمة المرور بنجاح',
    profileUpdated: 'تم تحديث الملف الشخصي بنجاح',
    settingsUpdated: 'تم تحديث الإعدادات بنجاح'
  }
};

export default translations;
