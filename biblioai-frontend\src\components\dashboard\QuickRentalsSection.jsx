import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

const QuickRentalsSection = () => {
  // Theme and Language hooks
  const { getCurrentColors } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();
  return (
    <section className="py-16 px-4 md:px-8 lg:px-20 transition-colors duration-200">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
        <div>
          <h3
            className="text-3xl font-medium transition-colors duration-200"
            style={{ color: colors.text }}
          >
            {t('quickRentals.title') || 'Quick Book Rentals:'}
            <br />
            <span style={{ color: colors.primary }}>
              {t('quickRentals.dive') || 'Dive'}
            </span> {t('quickRentals.into') || 'into'} <span style={{ color: colors.primary }}>
              {t('quickRentals.readingInstantly') || 'Reading Instantly'}
            </span>
          </h3>

          <p
            className="mt-6 text-base leading-relaxed transition-colors duration-200"
            style={{ color: colors.textSecondary }}
          >
            {t('quickRentals.description1') || 'Discover instant literary delight. Access a vast library, borrow your favorite reads, and dive into captivating stories within minutes. Reading made quick and easy, just a click away!'}
          </p>

          <p
            className="mt-6 text-base leading-relaxed transition-colors duration-200"
            style={{ color: colors.textSecondary }}
          >
            {t('quickRentals.description2') || 'Unlock a world of stories effortlessly. Browse genres, choose, rent in minutes. Seamlessly manage your reading adventures with our intuitive platform~'}
          </p>
        </div>
        
        <div>
          <img 
            src="/images/img_reading.png" 
            alt="Reading books" 
            className="w-full max-w-[600px] rounded-[10px]" 
          />
        </div>
      </div>
    </section>
  );
};

export default QuickRentalsSection;