import axios from 'axios';

const API_URL = 'http://localhost:5000/api/notifications';

// Get auth token
const getAuthToken = () => {
  return localStorage.getItem('token');
};

// Get auth headers
const getAuthHeaders = () => {
  const token = getAuthToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// Notification functions
export const getUserNotifications = async (page = 1, perPage = 10, unreadOnly = false) => {
  try {
    const params = { page, per_page: perPage };
    if (unreadOnly) params.unread_only = 'true';
    
    const response = await axios.get(API_URL, {
      headers: getAuthHeaders(),
      params
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

export const markNotificationRead = async (notificationId) => {
  try {
    const response = await axios.put(`${API_URL}/${notificationId}/read`, {}, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

export const markAllNotificationsRead = async () => {
  try {
    const response = await axios.put(`${API_URL}/mark-all-read`, {}, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

export const deleteNotification = async (notificationId) => {
  try {
    const response = await axios.delete(`${API_URL}/${notificationId}`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Utility functions
export const formatNotificationDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = now - date;
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
  const diffMinutes = Math.floor(diffTime / (1000 * 60));

  if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  } else if (diffMinutes > 0) {
    return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
  } else {
    return 'Just now';
  }
};

export const getNotificationIcon = (type) => {
  switch (type) {
    case 'reservation_created':
      return '📚';
    case 'borrowing_created':
      return '📖';
    case 'document_returned':
      return '✅';
    case 'borrowing_renewed':
      return '🔄';
    case 'due_reminder':
      return '⏰';
    case 'overdue_notice':
      return '⚠️';
    default:
      return '📢';
  }
};

export const getPriorityColor = (priority) => {
  switch (priority) {
    case 'urgent':
      return 'border-l-red-500 bg-red-50';
    case 'high':
      return 'border-l-orange-500 bg-orange-50';
    case 'normal':
      return 'border-l-blue-500 bg-blue-50';
    case 'low':
      return 'border-l-gray-500 bg-gray-50';
    default:
      return 'border-l-gray-500 bg-gray-50';
  }
};

export const getNotificationTypeLabel = (type) => {
  switch (type) {
    case 'reservation_created':
      return 'Reservation';
    case 'borrowing_created':
      return 'Borrowing';
    case 'document_returned':
      return 'Return';
    case 'borrowing_renewed':
      return 'Renewal';
    case 'due_reminder':
      return 'Due Reminder';
    case 'overdue_notice':
      return 'Overdue Notice';
    default:
      return 'Notification';
  }
};

// Real-time notification polling (optional)
export class NotificationPoller {
  constructor(callback, interval = 30000) { // Poll every 30 seconds
    this.callback = callback;
    this.interval = interval;
    this.isPolling = false;
    this.pollTimer = null;
  }

  start() {
    if (this.isPolling) return;
    
    this.isPolling = true;
    this.poll();
  }

  stop() {
    this.isPolling = false;
    if (this.pollTimer) {
      clearTimeout(this.pollTimer);
      this.pollTimer = null;
    }
  }

  async poll() {
    if (!this.isPolling) return;

    try {
      const notifications = await getUserNotifications(1, 5, true); // Get first 5 unread
      if (this.callback) {
        this.callback(notifications);
      }
    } catch (error) {
      console.error('Error polling notifications:', error);
    }

    if (this.isPolling) {
      this.pollTimer = setTimeout(() => this.poll(), this.interval);
    }
  }
}

// Notification sound (optional)
export const playNotificationSound = () => {
  try {
    const audio = new Audio('/notification-sound.mp3'); // Add sound file to public folder
    audio.volume = 0.3;
    audio.play().catch(e => console.log('Could not play notification sound:', e));
  } catch (error) {
    console.log('Notification sound not available');
  }
};

// Browser notification (optional)
export const showBrowserNotification = (title, message, icon = null) => {
  if (!('Notification' in window)) {
    console.log('This browser does not support desktop notification');
    return;
  }

  if (Notification.permission === 'granted') {
    new Notification(title, {
      body: message,
      icon: icon || '/favicon.ico',
      tag: 'biblioai-notification'
    });
  } else if (Notification.permission !== 'denied') {
    Notification.requestPermission().then(permission => {
      if (permission === 'granted') {
        new Notification(title, {
          body: message,
          icon: icon || '/favicon.ico',
          tag: 'biblioai-notification'
        });
      }
    });
  }
};

// Request notification permission
export const requestNotificationPermission = async () => {
  if (!('Notification' in window)) {
    return false;
  }

  if (Notification.permission === 'granted') {
    return true;
  }

  if (Notification.permission !== 'denied') {
    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }

  return false;
};
