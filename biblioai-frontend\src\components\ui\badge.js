import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';

export const Badge = ({
  children,
  variant = 'default',
  className = '',
  ...props
}) => {
  const { getCurrentColors, isDarkMode } = useTheme();
  const colors = getCurrentColors();

  const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200';

  const getVariantStyles = () => {
    const lightBg = isDarkMode ? 0.2 : 0.1;
    const darkBg = isDarkMode ? 0.8 : 0.9;

    switch (variant) {
      case 'default':
        return {
          backgroundColor: isDarkMode ? '#1e40af20' : '#dbeafe',
          color: colors.primary
        };
      case 'secondary':
        return {
          backgroundColor: colors.surface,
          color: colors.textSecondary,
          border: `1px solid ${colors.border}`
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          color: colors.text,
          border: `1px solid ${colors.border}`
        };
      case 'success':
        return {
          backgroundColor: isDarkMode ? '#16a34a20' : '#dcfce7',
          color: colors.success
        };
      case 'warning':
        return {
          backgroundColor: isDarkMode ? '#ca8a0420' : '#fef3c7',
          color: colors.warning
        };
      case 'error':
        return {
          backgroundColor: isDarkMode ? '#dc262620' : '#fee2e2',
          color: colors.error
        };
      default:
        return {
          backgroundColor: isDarkMode ? '#1e40af20' : '#dbeafe',
          color: colors.primary
        };
    }
  };

  return (
    <span
      className={`${baseClasses} ${className}`}
      style={getVariantStyles()}
      {...props}
    >
      {children}
    </span>
  );
};
