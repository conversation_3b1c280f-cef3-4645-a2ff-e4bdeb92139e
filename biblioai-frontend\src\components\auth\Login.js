import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { login } from '../../services/authService';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import './Auth.css';

const Login = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  // Theme and Language hooks
  const { getCurrentColors, toggleTheme, isDarkMode } = useTheme();
  const { t, changeLanguage, currentLanguage } = useLanguage();
  const colors = getCurrentColors();

  const { username, password } = formData;

  const onChange = e => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const onSubmit = async e => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Call the actual auth service
      const response = await login(formData);

      if (response.access_token && response.user) {
        // Token and user data are already stored by authService
        setLoading(false);

        // Redirect based on user role
        const userRole = response.user.role;
        switch (userRole) {
          case 'librarian':
            navigate('/librarian-dashboard');
            break;
          case 'member':
            navigate('/member-dashboard');
            break;
          case 'manager':
            navigate('/librarian-dashboard'); // Managers can use librarian dashboard
            break;
          case 'clerk':
            navigate('/member-dashboard'); // Clerks can use member dashboard for now
            break;
          default:
            navigate('/member-dashboard');
        }
      }

    } catch (err) {
      setError(err.message || 'Invalid username or password');
      setLoading(false);
    }
  };

  return (
    <div
      className="auth-container transition-colors duration-200"
      style={{ backgroundColor: colors.background }}
    >
      {/* Theme and Language Controls */}
      <div className="absolute top-4 right-4 flex items-center space-x-3">
        <button
          onClick={toggleTheme}
          className="p-2 rounded-lg transition-colors duration-200"
          style={{
            backgroundColor: `${colors.primary}20`,
            color: colors.text
          }}
          title={isDarkMode ? t('settings.lightMode') : t('settings.darkMode')}
        >
          {isDarkMode ? '☀️' : '🌙'}
        </button>

        <button
          onClick={() => changeLanguage(currentLanguage === 'en' ? 'fr' : 'en')}
          className="p-2 rounded-lg transition-colors duration-200 text-sm font-medium"
          style={{
            backgroundColor: `${colors.primary}20`,
            color: colors.text
          }}
          title={t('settings.changeLanguage')}
        >
          {currentLanguage === 'en' ? '🇫🇷 FR' : '🇺🇸 EN'}
        </button>
      </div>

      <div
        className="auth-card transition-colors duration-200"
        style={{
          backgroundColor: colors.surface,
          borderColor: colors.border
        }}
      >
        <h2 style={{ color: colors.text }}>
          {t('auth.login')} to BiblioAI
        </h2>
        {error && (
          <div
            className="alert-error transition-colors duration-200"
            style={{
              backgroundColor: isDarkMode ? '#7f1d1d' : '#fef2f2',
              color: colors.error,
              borderColor: colors.error
            }}
          >
            {error}
          </div>
        )}
        <form onSubmit={onSubmit}>
          <div className="form-group">
            <label
              htmlFor="username"
              style={{ color: colors.text }}
            >
              {t('auth.username')}
            </label>
            <input
              type="text"
              id="username"
              name="username"
              value={username}
              onChange={onChange}
              required
              style={{
                backgroundColor: colors.background,
                borderColor: colors.border,
                color: colors.text
              }}
              className="transition-colors duration-200"
            />
          </div>
          <div className="form-group">
            <label
              htmlFor="password"
              style={{ color: colors.text }}
            >
              {t('auth.password')}
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={password}
              onChange={onChange}
              required
              style={{
                backgroundColor: colors.background,
                borderColor: colors.border,
                color: colors.text
              }}
              className="transition-colors duration-200"
            />
          </div>
          <button
            type="submit"
            className="btn-primary transition-colors duration-200"
            disabled={loading}
            style={{ backgroundColor: colors.primary }}
          >
            {loading ? t('common.loading') : t('auth.login')}
          </button>
        </form>
        <p
          className="auth-link transition-colors duration-200"
          style={{ color: colors.textSecondary }}
        >
          {t('auth.noAccount') || "Don't have an account?"} <a
            href="/register"
            style={{ color: colors.primary }}
          >
            {t('auth.register')}
          </a>
        </p>
      </div>
    </div>
  );
};

export default Login;
