import React, { createContext, useContext, useState, useEffect } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    // Provide fallback values instead of throwing error
    console.warn('useLanguage must be used within a LanguageProvider. Using fallback values.');
    return {
      currentLanguage: 'en',
      changeLanguage: () => {},
      t: (key) => key,
      isLoading: false,
      supportedLanguages: SUPPORTED_LANGUAGES,
      getCurrentLanguage: () => SUPPORTED_LANGUAGES.en,
      isRTL: () => false
    };
  }
  return context;
};

// Supported languages
export const SUPPORTED_LANGUAGES = {
  en: {
    code: 'en',
    name: 'English',
    flag: '🇺🇸',
    direction: 'ltr'
  },
  fr: {
    code: 'fr',
    name: 'Français',
    flag: '🇫🇷',
    direction: 'ltr'
  },
  es: {
    code: 'es',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    flag: '🇪🇸',
    direction: 'ltr'
  },
  ar: {
    code: 'ar',
    name: 'العربية',
    flag: '🇸🇦',
    direction: 'rtl'
  },
  de: {
    code: 'de',
    name: 'Deutsch',
    flag: '🇩🇪',
    direction: 'ltr'
  },
  zh: {
    code: 'zh',
    name: '中文',
    flag: '🇨🇳',
    direction: 'ltr'
  }
};

export const LanguageProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState(() => {
    try {
      // Check localStorage first, then browser language, then default to English
      const savedLanguage = localStorage.getItem('language');
      if (savedLanguage && SUPPORTED_LANGUAGES[savedLanguage]) {
        return savedLanguage;
      }

      // Check browser language
      const browserLang = navigator.language.split('-')[0];
      if (SUPPORTED_LANGUAGES[browserLang]) {
        return browserLang;
      }

      return 'en'; // Default to English
    } catch (error) {
      console.error('Error getting initial language:', error);
      return 'en';
    }
  });

  const [translations, setTranslations] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  // Load translations
  useEffect(() => {
    loadTranslations(currentLanguage);
  }, [currentLanguage]);

  // Apply language direction
  useEffect(() => {
    const language = SUPPORTED_LANGUAGES[currentLanguage];
    document.documentElement.dir = language.direction;
    document.documentElement.lang = language.code;
    localStorage.setItem('language', currentLanguage);
  }, [currentLanguage]);

  const loadTranslations = async (languageCode) => {
    setIsLoading(true);
    try {
      // Import translation file
      const translationModule = await import(`../translations/${languageCode}.js`);
      setTranslations(translationModule.default || {});
    } catch (error) {
      console.error(`Failed to load translations for ${languageCode}:`, error);
      // Fallback to English if translation fails
      if (languageCode !== 'en') {
        try {
          const fallbackModule = await import('../translations/en.js');
          setTranslations(fallbackModule.default || {});
        } catch (fallbackError) {
          console.error('Failed to load fallback translations:', fallbackError);
          // Set basic fallback translations
          setTranslations({
            common: { loading: 'Loading...', error: 'Error' },
            nav: { dashboard: 'Dashboard' },
            settings: { title: 'Settings', darkMode: 'Dark Mode', lightMode: 'Light Mode' }
          });
        }
      } else {
        // If English fails, set basic fallback
        setTranslations({
          common: { loading: 'Loading...', error: 'Error' },
          nav: { dashboard: 'Dashboard' },
          settings: { title: 'Settings', darkMode: 'Dark Mode', lightMode: 'Light Mode' }
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const changeLanguage = (languageCode) => {
    if (SUPPORTED_LANGUAGES[languageCode] && languageCode !== currentLanguage) {
      setCurrentLanguage(languageCode);
    }
  };

  // Translation function with fallback
  const t = (key, params = {}) => {
    if (isLoading) {
      // Return a reasonable fallback during loading
      const fallbacks = {
        'common.loading': 'Loading...',
        'nav.dashboard': 'Dashboard',
        'settings.title': 'Settings',
        'settings.darkMode': 'Dark Mode',
        'settings.lightMode': 'Light Mode',
        'dashboard.overview': 'Overview',
        'books.searchBooks': 'Search Books',
        'ai.recommendations': 'AI Recommendations',
        'borrowing.myBorrowings': 'My Borrowings',
        'reservations.myReservations': 'My Reservations',
        'notifications.title': 'Notifications'
      };
      return fallbacks[key] || key;
    }

    if (!translations || typeof translations !== 'object') {
      return key;
    }

    const keys = key.split('.');
    let value = translations;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // Return key if translation not found
        return key;
      }
    }

    if (typeof value !== 'string') {
      return key;
    }

    // Replace parameters in translation
    let result = value;
    if (params && typeof params === 'object') {
      Object.keys(params).forEach(param => {
        result = result.replace(new RegExp(`{{${param}}}`, 'g'), params[param]);
      });
    }

    return result;
  };

  const value = {
    currentLanguage,
    changeLanguage,
    t,
    isLoading,
    supportedLanguages: SUPPORTED_LANGUAGES,
    getCurrentLanguage: () => SUPPORTED_LANGUAGES[currentLanguage],
    isRTL: () => SUPPORTED_LANGUAGES[currentLanguage].direction === 'rtl'
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
