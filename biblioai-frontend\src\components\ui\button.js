import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';

export const Button = ({
  children,
  variant = 'default',
  size = 'default',
  className = '',
  disabled = false,
  ...props
}) => {
  const { getCurrentColors } = useTheme();
  const colors = getCurrentColors();

  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';

  const getVariantStyles = () => {
    switch (variant) {
      case 'default':
        return {
          backgroundColor: colors.primary,
          color: 'white',
          borderColor: colors.primary
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          color: colors.text,
          borderColor: colors.border,
          border: '1px solid'
        };
      case 'ghost':
        return {
          backgroundColor: 'transparent',
          color: colors.text
        };
      case 'secondary':
        return {
          backgroundColor: colors.surface,
          color: colors.text,
          borderColor: colors.border
        };
      default:
        return {
          backgroundColor: colors.primary,
          color: 'white'
        };
    }
  };

  const sizes = {
    default: 'h-10 py-2 px-4 text-sm',
    sm: 'h-8 px-3 text-xs',
    lg: 'h-12 px-8 text-base'
  };

  return (
    <button
      className={`${baseClasses} ${sizes[size]} ${className}`}
      style={getVariantStyles()}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
};
