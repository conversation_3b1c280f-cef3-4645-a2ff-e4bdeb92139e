import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';

export const Card = ({ children, className = '', ...props }) => {
  const { getCurrentColors } = useTheme();
  const colors = getCurrentColors();

  return (
    <div
      className={`shadow-md rounded-lg border transition-colors duration-200 ${className}`}
      style={{
        backgroundColor: colors.surface,
        borderColor: colors.border
      }}
      {...props}
    >
      {children}
    </div>
  );
};

export const CardHeader = ({ children, className = '', ...props }) => {
  const { getCurrentColors } = useTheme();
  const colors = getCurrentColors();

  return (
    <div
      className={`px-6 py-4 border-b transition-colors duration-200 ${className}`}
      style={{ borderBottomColor: colors.border }}
      {...props}
    >
      {children}
    </div>
  );
};

export const CardTitle = ({ children, className = '', ...props }) => {
  const { getCurrentColors } = useTheme();
  const colors = getCurrentColors();

  return (
    <h3
      className={`text-lg font-semibold transition-colors duration-200 ${className}`}
      style={{ color: colors.text }}
      {...props}
    >
      {children}
    </h3>
  );
};

export const CardContent = ({ children, className = '', ...props }) => {
  return (
    <div
      className={`px-6 py-4 ${className}`}
      {...props}
    >
      {children}
    </div>
  );
};
