#!/usr/bin/env python3
"""
AI-powered book recommendation system for members
"""

import requests
import json
from datetime import datetime, timedelta
from collections import Counter
import openai
import os

class AIRecommendationEngine:
    def __init__(self):
        # You can use OpenAI API, Hugging Face, or local models
        self.openai_api_key = os.environ.get('OPENAI_API_KEY')
        if self.openai_api_key:
            openai.api_key = self.openai_api_key
    
    def get_user_reading_history(self, user_id):
        """Get user's reading history and preferences"""
        try:
            # This would connect to your database
            from app import db, Borrowing, MemberHistory
            
            # Get recent borrowings
            recent_borrowings = Borrowing.query.filter_by(
                user_id=user_id
            ).order_by(Borrowing.created_at.desc()).limit(20).all()
            
            # Get reading history
            reading_history = MemberHistory.query.filter_by(
                user_id=user_id,
                action_type='borrow'
            ).order_by(MemberHistory.created_at.desc()).limit(50).all()
            
            return {
                'recent_borrowings': recent_borrowings,
                'reading_history': reading_history
            }
        except Exception as e:
            print(f"Error getting reading history: {e}")
            return {'recent_borrowings': [], 'reading_history': []}
    
    def analyze_reading_preferences(self, user_id):
        """Analyze user's reading preferences using AI"""
        history = self.get_user_reading_history(user_id)
        
        # Extract categories, subjects, and authors
        categories = []
        subjects = []
        authors = []
        
        for borrowing in history['recent_borrowings']:
            # Get document info (you'd call document service here)
            doc_info = self.get_document_info(borrowing.document_id)
            if doc_info:
                if doc_info.get('category'):
                    categories.append(doc_info['category'])
                if doc_info.get('subject'):
                    subjects.append(doc_info['subject'])
                if doc_info.get('author'):
                    authors.append(doc_info['author'])
        
        # Count preferences
        preferred_categories = Counter(categories).most_common(3)
        preferred_subjects = Counter(subjects).most_common(3)
        preferred_authors = Counter(authors).most_common(3)
        
        return {
            'categories': preferred_categories,
            'subjects': preferred_subjects,
            'authors': preferred_authors
        }
    
    def get_ai_recommendations(self, user_id, preferences, available_books):
        """Get AI-powered recommendations"""
        if not self.openai_api_key:
            return self.get_rule_based_recommendations(preferences, available_books)
        
        try:
            # Prepare context for AI
            context = f"""
            User Reading Preferences:
            - Favorite Categories: {[cat[0] for cat in preferences['categories']]}
            - Favorite Subjects: {[subj[0] for subj in preferences['subjects']]}
            - Favorite Authors: <AUTHORS>
            
            Available Books:
            {json.dumps(available_books[:20], indent=2)}
            
            Please recommend 5 books from the available list that match the user's preferences.
            Consider genre similarity, author style, and subject matter.
            Return as JSON with book IDs and reasons.
            """
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a librarian AI that recommends books based on user preferences."},
                    {"role": "user", "content": context}
                ],
                max_tokens=500
            )
            
            ai_response = response.choices[0].message.content
            return self.parse_ai_recommendations(ai_response, available_books)
            
        except Exception as e:
            print(f"AI recommendation error: {e}")
            return self.get_rule_based_recommendations(preferences, available_books)
    
    def get_rule_based_recommendations(self, preferences, available_books):
        """Fallback rule-based recommendations"""
        recommendations = []
        
        # Score books based on preferences
        for book in available_books:
            score = 0
            reasons = []
            
            # Category matching
            for cat, count in preferences['categories']:
                if book.get('category') == cat:
                    score += count * 3
                    reasons.append(f"Matches your favorite category: {cat}")
            
            # Subject matching
            for subj, count in preferences['subjects']:
                if book.get('subject') == subj:
                    score += count * 2
                    reasons.append(f"Matches your interest in: {subj}")
            
            # Author matching
            for auth, count in preferences['authors']:
                if book.get('author') == auth:
                    score += count * 4
                    reasons.append(f"By your favorite author: {auth}")
            
            if score > 0:
                recommendations.append({
                    'book': book,
                    'score': score,
                    'reasons': reasons
                })
        
        # Sort by score and return top 5
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        return recommendations[:5]
    
    def get_document_info(self, document_id):
        """Get document information from document service"""
        try:
            response = requests.get(f"http://localhost:5005/documents/{document_id}")
            if response.status_code == 200:
                return response.json()
            return None
        except:
            return None
    
    def parse_ai_recommendations(self, ai_response, available_books):
        """Parse AI response into structured recommendations"""
        try:
            # Try to extract JSON from AI response
            import re
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                ai_data = json.loads(json_match.group())
                return ai_data.get('recommendations', [])
            return []
        except:
            return []

# Integration functions for the main app
def get_personalized_recommendations(user_id, limit=5):
    """Main function to get personalized recommendations for a user"""
    try:
        engine = AIRecommendationEngine()
        
        # Get user preferences
        preferences = engine.analyze_reading_preferences(user_id)
        
        # Get available books (call document service)
        response = requests.get("http://localhost:5005/documents")
        if response.status_code == 200:
            available_books = response.json().get('documents', [])
            # Filter only available books
            available_books = [book for book in available_books if book.get('available_copies', 0) > 0]
        else:
            available_books = []
        
        # Get AI recommendations
        recommendations = engine.get_ai_recommendations(user_id, preferences, available_books)
        
        return {
            'success': True,
            'recommendations': recommendations,
            'preferences': preferences
        }
        
    except Exception as e:
        print(f"Error getting recommendations: {e}")
        return {
            'success': False,
            'error': str(e),
            'recommendations': []
        }

def get_trending_books():
    """Get trending books based on borrowing patterns"""
    try:
        from app import db, Borrowing
        from sqlalchemy import func
        
        # Get most borrowed books in last 30 days
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        
        trending = db.session.query(
            Borrowing.document_id,
            func.count(Borrowing.id).label('borrow_count')
        ).filter(
            Borrowing.created_at >= thirty_days_ago
        ).group_by(
            Borrowing.document_id
        ).order_by(
            func.count(Borrowing.id).desc()
        ).limit(10).all()
        
        # Get document details for trending books
        trending_books = []
        for doc_id, count in trending:
            response = requests.get(f"http://localhost:5005/documents/{doc_id}")
            if response.status_code == 200:
                book_info = response.json()
                book_info['borrow_count'] = count
                trending_books.append(book_info)
        
        return trending_books
        
    except Exception as e:
        print(f"Error getting trending books: {e}")
        return []
