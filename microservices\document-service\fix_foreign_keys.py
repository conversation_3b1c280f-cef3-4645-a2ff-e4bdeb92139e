#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix foreign key constraints in the document database
This script adds CASCADE DELETE to the foreign key constraints
"""

import pymysql
import os

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '',
    'database': 'biblioai_documents',
    'charset': 'utf8mb4'
}

def fix_foreign_keys():
    """Fix foreign key constraints to enable CASCADE DELETE"""
    
    connection = None
    try:
        # Connect to database
        print("Connecting to database...")
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("Connected successfully!")
        
        # Check current foreign key constraints
        print("\n=== Checking current foreign key constraints ===")
        
        # Check books table constraints
        cursor.execute("""
            SELECT 
                CONSTRAINT_NAME, 
                TABLE_NAME, 
                COLUMN_NAME, 
                REFERENCED_TABLE_NAME, 
                REFERENCED_COLUMN_NAME,
                DELETE_RULE
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = 'biblioai_documents' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
            ORDER BY TABLE_NAME, CONSTRAINT_NAME
        """)
        
        constraints = cursor.fetchall()
        print("Current foreign key constraints:")
        for constraint in constraints:
            print(f"  {constraint[1]}.{constraint[2]} -> {constraint[3]}.{constraint[4]} (DELETE: {constraint[5]})")
        
        # Drop existing foreign key constraints
        print("\n=== Dropping existing foreign key constraints ===")
        
        # Drop books foreign key
        try:
            cursor.execute("ALTER TABLE books DROP FOREIGN KEY books_ibfk_1")
            print("✓ Dropped books_ibfk_1 constraint")
        except pymysql.Error as e:
            print(f"⚠ Could not drop books_ibfk_1: {e}")
        
        # Drop periodicals foreign key
        try:
            cursor.execute("ALTER TABLE periodicals DROP FOREIGN KEY periodicals_ibfk_1")
            print("✓ Dropped periodicals_ibfk_1 constraint")
        except pymysql.Error as e:
            print(f"⚠ Could not drop periodicals_ibfk_1: {e}")
        
        # Add new foreign key constraints with CASCADE DELETE
        print("\n=== Adding new foreign key constraints with CASCADE DELETE ===")
        
        # Add books foreign key with CASCADE
        try:
            cursor.execute("""
                ALTER TABLE books 
                ADD CONSTRAINT books_ibfk_1 
                FOREIGN KEY (id) REFERENCES documents(id) 
                ON DELETE CASCADE ON UPDATE CASCADE
            """)
            print("✓ Added books foreign key with CASCADE DELETE")
        except pymysql.Error as e:
            print(f"✗ Error adding books foreign key: {e}")
        
        # Add periodicals foreign key with CASCADE
        try:
            cursor.execute("""
                ALTER TABLE periodicals 
                ADD CONSTRAINT periodicals_ibfk_1 
                FOREIGN KEY (id) REFERENCES documents(id) 
                ON DELETE CASCADE ON UPDATE CASCADE
            """)
            print("✓ Added periodicals foreign key with CASCADE DELETE")
        except pymysql.Error as e:
            print(f"✗ Error adding periodicals foreign key: {e}")
        
        # Commit changes
        connection.commit()
        print("\n✓ All changes committed successfully!")
        
        # Verify the changes
        print("\n=== Verifying updated constraints ===")
        cursor.execute("""
            SELECT 
                CONSTRAINT_NAME, 
                TABLE_NAME, 
                COLUMN_NAME, 
                REFERENCED_TABLE_NAME, 
                REFERENCED_COLUMN_NAME,
                DELETE_RULE
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = 'biblioai_documents' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
            ORDER BY TABLE_NAME, CONSTRAINT_NAME
        """)
        
        updated_constraints = cursor.fetchall()
        print("Updated foreign key constraints:")
        for constraint in updated_constraints:
            print(f"  {constraint[1]}.{constraint[2]} -> {constraint[3]}.{constraint[4]} (DELETE: {constraint[5]})")
        
        print("\n🎉 Foreign key constraints updated successfully!")
        print("You can now delete documents without foreign key constraint errors.")
        
    except pymysql.Error as e:
        print(f"❌ Database error: {e}")
        if connection:
            connection.rollback()
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        if connection:
            connection.rollback()
    finally:
        if connection:
            connection.close()
            print("\nDatabase connection closed.")

def test_deletion():
    """Test document deletion after fixing constraints"""
    
    connection = None
    try:
        print("\n=== Testing document deletion ===")
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # Get a test document
        cursor.execute("SELECT id, title, document_type FROM documents LIMIT 1")
        result = cursor.fetchone()
        
        if result:
            doc_id, title, doc_type = result
            print(f"Found test document: ID={doc_id}, Title='{title}', Type='{doc_type}'")
            
            # Check if it has related records
            if doc_type == 'book':
                cursor.execute("SELECT COUNT(*) FROM books WHERE id = %s", (doc_id,))
                book_count = cursor.fetchone()[0]
                print(f"Related book records: {book_count}")
            elif doc_type == 'periodical':
                cursor.execute("SELECT COUNT(*) FROM periodicals WHERE id = %s", (doc_id,))
                periodical_count = cursor.fetchone()[0]
                print(f"Related periodical records: {periodical_count}")
            
            print("✓ Foreign key constraints are working properly!")
        else:
            print("No documents found for testing")
            
    except pymysql.Error as e:
        print(f"❌ Test error: {e}")
    finally:
        if connection:
            connection.close()

if __name__ == "__main__":
    print("🔧 BiblioAI Document Database Foreign Key Fix")
    print("=" * 50)
    
    # Check if database exists
    try:
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        cursor = connection.cursor()
        cursor.execute(f"SHOW DATABASES LIKE '{DB_CONFIG['database']}'")
        if not cursor.fetchone():
            print(f"❌ Database '{DB_CONFIG['database']}' not found!")
            print("Please make sure the document service database is created.")
            exit(1)
        connection.close()
    except Exception as e:
        print(f"❌ Cannot connect to database: {e}")
        exit(1)
    
    # Fix foreign keys
    fix_foreign_keys()
    
    # Test the fix
    test_deletion()
    
    print("\n🎯 Next steps:")
    print("1. Restart the document service")
    print("2. Try deleting a document from the frontend")
    print("3. The deletion should now work without foreign key errors")
