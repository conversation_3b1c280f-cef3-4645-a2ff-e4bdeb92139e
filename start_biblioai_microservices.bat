@echo off
echo ========================================
echo    BiblioAI Microservices Launcher
echo ========================================
echo.

echo Demarrage des microservices BiblioAI...
echo.

echo IMPORTANT: Assurez-vous que MySQL est demarré !
echo.
pause

echo Demarrage Auth Service (Port 5001)...
start "Auth Service" cmd /k "cd microservices\auth-service && start.bat"
timeout /t 5

echo Demarrage Document Service (Port 5002)...
start "Document Service" cmd /k "cd microservices\document-service && start.bat"
timeout /t 5

echo Demarrage Classification Service (Port 5003)...
start "Classification Service" cmd /k "cd microservices\classification-service && start.bat"
timeout /t 5

echo Demarrage Search Service (Port 5004)...
start "Search Service" cmd /k "cd microservices\search-service && start.bat"
timeout /t 5

echo Demarrage Member Service (Port 5006)...
start "Member Service" cmd /k "cd microservices\member-service && start.bat"
timeout /t 5

echo Demarrage Notification Service (Port 5007)...
start "Notification Service" cmd /k "cd microservices\notification-service && start.bat"
timeout /t 5

echo Demarrage API Gateway (Port 5000)...
start "API Gateway" cmd /k "cd microservices\api-gateway && start.bat"
timeout /t 5

echo.
echo ========================================
echo   Tous les services sont en cours de demarrage !
echo ========================================
echo.
echo Services:
echo - API Gateway: http://localhost:5000
echo - Auth Service: http://localhost:5001
echo - Document Service: http://localhost:5005
echo - Classification Service: http://localhost:5003
echo - Search Service: http://localhost:5004
- Member Service: http://localhost:5006
- Notification Service: http://localhost:5007
echo.
echo Attendez quelques secondes puis testez avec:
echo   cd microservices
echo   python check_services.py
echo.
echo Pour demarrer le frontend:
echo   cd biblioai-frontend
echo   npm start
echo.
pause
