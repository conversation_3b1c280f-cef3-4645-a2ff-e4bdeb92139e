from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import J<PERSON><PERSON><PERSON><PERSON>, create_access_token, jwt_required, get_jwt_identity
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import os
import re

app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'auth-service-secret')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get(
    'DATABASE_URI',
    'mysql+pymysql://root:@localhost:3306/biblioai_auth'  # XAMPP MySQL configuration
)
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET_KEY', 'biblioai-jwt-secret-key-2024')

# Initialize extensions
db = SQLAlchemy(app)
jwt = JWTManager(app)
CORS(app)

# User Model
class User(db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    role = db.Column(db.String(20), nullable=False, default='member')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'created_at': self.created_at.isoformat(),
            'is_active': self.is_active
        }

# Routes
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'service': 'auth-service'}), 200

@app.route('/register', methods=['POST'])
def register():
    data = request.get_json()

    # Validate required fields
    if not all(k in data for k in ('username', 'email', 'password', 'role')):
        return jsonify({'message': 'Missing required fields'}), 400

    # Validate email format
    email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_regex, data['email']):
        return jsonify({'message': 'Invalid email format'}), 400

    # Check if user already exists
    if User.query.filter_by(username=data['username']).first():
        return jsonify({'message': 'Username already exists'}), 409

    if User.query.filter_by(email=data['email']).first():
        return jsonify({'message': 'Email already exists'}), 409

    # Validate role
    valid_roles = ['member', 'librarian', 'clerk', 'manager']
    if data['role'] not in valid_roles:
        return jsonify({'message': 'Invalid role'}), 400

    # Create new user
    user = User(
        username=data['username'],
        email=data['email'],
        role=data['role']
    )
    user.set_password(data['password'])

    db.session.add(user)
    db.session.commit()

    return jsonify({'message': 'User registered successfully'}), 201

@app.route('/login', methods=['POST'])
def login():
    data = request.get_json()

    # Validate required fields
    if not all(k in data for k in ('username', 'password')):
        return jsonify({'message': 'Missing username or password'}), 400

    # Find user by username
    user = User.query.filter_by(username=data['username']).first()

    # Check if user exists and password is correct
    if not user or not user.check_password(data['password']):
        return jsonify({'message': 'Invalid username or password'}), 401

    if not user.is_active:
        return jsonify({'message': 'Account is deactivated'}), 401

    # Create access token
    access_token = create_access_token(
        identity=str(user.id),  # Convert to string for JWT compatibility
        additional_claims={'role': user.role, 'username': user.username},
        expires_delta=timedelta(days=1)
    )

    return jsonify({
        'access_token': access_token,
        'user': user.to_dict()
    }), 200

@app.route('/verify', methods=['GET'])
@jwt_required()
def verify_token():
    user_id = int(get_jwt_identity())  # Convert back to integer
    user = User.query.get(user_id)

    if not user or not user.is_active:
        return jsonify({'message': 'Invalid or inactive user'}), 401

    return jsonify({
        'valid': True,
        'user': user.to_dict()
    }), 200

@app.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    user_id = int(get_jwt_identity())  # Convert back to integer
    user = User.query.get(user_id)

    if not user:
        return jsonify({'message': 'User not found'}), 404

    return jsonify(user.to_dict()), 200

@app.route('/users', methods=['GET'])
@jwt_required()
def get_users():
    # Only managers can view all users
    from flask_jwt_extended import get_jwt
    claims = get_jwt()
    if claims.get('role') not in ['manager']:
        return jsonify({'message': 'Insufficient permissions'}), 403

    users = User.query.all()
    return jsonify([user.to_dict() for user in users]), 200

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'message': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'message': 'Internal server error'}), 500

# Initialize database with demo users
def init_demo_users():
    """Initialize demo users for testing"""
    if User.query.count() == 0:
        demo_users = [
            {
                'username': 'demo_librarian',
                'email': '<EMAIL>',
                'password': 'demo123',
                'role': 'librarian'
            },
            {
                'username': 'demo_manager',
                'email': '<EMAIL>',
                'password': 'demo123',
                'role': 'manager'
            },
            {
                'username': 'demo_member',
                'email': '<EMAIL>',
                'password': 'demo123',
                'role': 'member'
            },
            {
                'username': 'demo_clerk',
                'email': '<EMAIL>',
                'password': 'demo123',
                'role': 'clerk'
            }
        ]

        for user_data in demo_users:
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                role=user_data['role']
            )
            user.set_password(user_data['password'])
            db.session.add(user)

        db.session.commit()
        print("Demo users created successfully!")

# Initialize database
with app.app_context():
    db.create_all()
    init_demo_users()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
