import React, { useState, useEffect } from 'react';
import { getUserBorrowings, renewBorrowing, formatDate, getStatusColor, getDaysUntilDue, isOverdue } from '../../services/memberService';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

const BorrowingList = () => {
  const [borrowings, setBorrowings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [renewingId, setRenewingId] = useState(null);

  // Theme and Language hooks
  const { getCurrentColors } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();

  useEffect(() => {
    fetchBorrowings();
  }, []);

  const fetchBorrowings = async () => {
    try {
      setLoading(true);
      const data = await getUserBorrowings();
      setBorrowings(data.borrowings || []);
      setError('');
    } catch (err) {
      setError(err.message || 'Failed to fetch borrowings');
    } finally {
      setLoading(false);
    }
  };

  const handleRenewBorrowing = async (borrowingId) => {
    if (!window.confirm('Are you sure you want to renew this borrowing?')) {
      return;
    }

    try {
      setRenewingId(borrowingId);
      await renewBorrowing(borrowingId);
      await fetchBorrowings(); // Refresh the list
    } catch (err) {
      setError(err.message || 'Failed to renew borrowing');
    } finally {
      setRenewingId(null);
    }
  };

  const getDueDateStatus = (borrowing) => {
    if (borrowing.status !== 'active') return null;
    
    const daysUntilDue = getDaysUntilDue(borrowing.due_date);
    if (daysUntilDue === null) return null;

    if (daysUntilDue < 0) {
      return {
        text: `${Math.abs(daysUntilDue)} days overdue`,
        color: 'text-red-600 bg-red-100'
      };
    } else if (daysUntilDue <= 3) {
      return {
        text: `Due in ${daysUntilDue} day${daysUntilDue !== 1 ? 's' : ''}`,
        color: 'text-orange-600 bg-orange-100'
      };
    } else {
      return {
        text: `Due in ${daysUntilDue} days`,
        color: 'text-green-600 bg-green-100'
      };
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (borrowings.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="mx-auto h-12 w-12 text-gray-400">
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        </div>
        <h3 className="mt-2 text-sm font-medium text-gray-900">No borrowings</h3>
        <p className="mt-1 text-sm text-gray-500">You haven't borrowed any documents yet.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">My Borrowings</h3>
        <button
          onClick={fetchBorrowings}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh
        </button>
      </div>

      <div
        className="shadow overflow-hidden sm:rounded-md transition-colors duration-200"
        style={{ backgroundColor: colors.surface }}
      >
        <ul
          className="divide-y transition-colors duration-200"
          style={{ borderColor: colors.border }}
        >
          {borrowings.map((borrowing) => {
            const dueDateStatus = getDueDateStatus(borrowing);
            const canRenew = borrowing.status === 'active' && borrowing.renewal_count < borrowing.max_renewals;
            
            return (
              <li key={borrowing.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-indigo-600 truncate">
                          {borrowing.document?.title || 'Unknown Document'}
                        </p>
                        <div className="ml-2 flex-shrink-0 flex space-x-2">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(borrowing.status)}`}>
                            {borrowing.status}
                          </span>
                          {dueDateStatus && (
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${dueDateStatus.color}`}>
                              {dueDateStatus.text}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="mt-2 sm:flex sm:justify-between">
                        <div className="sm:flex">
                          <p className="flex items-center text-sm text-gray-500">
                            <svg className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            {borrowing.document?.author || 'Unknown Author'}
                          </p>
                          <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                            <svg className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Renewals: {borrowing.renewal_count}/{borrowing.max_renewals}
                          </p>
                        </div>
                        <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                          <svg className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8m-8 0H4a2 2 0 00-2 2v6a2 2 0 002 2h16a2 2 0 002-2v-6a2 2 0 00-2-2h-4" />
                          </svg>
                          Borrowed: {formatDate(borrowing.borrow_date)}
                        </div>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500">
                        <svg className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Due: {formatDate(borrowing.due_date)}
                      </div>
                      {borrowing.return_date && (
                        <div className="mt-2 flex items-center text-sm text-gray-500">
                          <svg className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          Returned: {formatDate(borrowing.return_date)}
                        </div>
                      )}
                      {borrowing.fine_amount > 0 && (
                        <div className="mt-2 flex items-center text-sm text-red-600">
                          <svg className="flex-shrink-0 mr-1.5 h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                          </svg>
                          Fine: ${borrowing.fine_amount.toFixed(2)}
                        </div>
                      )}
                      {borrowing.notes && (
                        <div className="mt-2">
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Notes:</span> {borrowing.notes}
                          </p>
                        </div>
                      )}
                    </div>
                    {canRenew && (
                      <div className="ml-4 flex-shrink-0">
                        <button
                          onClick={() => handleRenewBorrowing(borrowing.id)}
                          disabled={renewingId === borrowing.id}
                          className="inline-flex items-center px-3 py-2 border border-green-300 shadow-sm text-sm leading-4 font-medium rounded-md text-green-700 bg-white hover:bg-green-50 disabled:opacity-50"
                        >
                          {renewingId === borrowing.id ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600 mr-2"></div>
                              Renewing...
                            </>
                          ) : (
                            <>
                              <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                              </svg>
                              Renew
                            </>
                          )}
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};

export default BorrowingList;
