import React, { createContext, useState, useContext, useCallback } from 'react';
import { getDocuments, createDocument, updateDocument, deleteDocument } from '../services/documentService';

const DocumentContext = createContext();

export const useDocuments = () => useContext(DocumentContext);

export const DocumentProvider = ({ children }) => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({});

  const fetchDocuments = useCallback(async (page = currentPage, filterParams = filters) => {
    try {
      setLoading(true);
      const params = {
        page: page,
        per_page: 10,
        ...filterParams
      };

      const response = await getDocuments(params);
      setDocuments(response.documents);
      setTotalPages(response.pages);
      setError('');
      return response.documents;
    } catch (err) {
      setError(err.message || 'Failed to fetch documents');
      return [];
    } finally {
      setLoading(false);
    }
  }, [currentPage, filters]);

  const addDocument = async (documentData) => {
    try {
      setLoading(true);
      const result = await createDocument(documentData);
      
      // Add the new document to the list
      if (result.document) {
        setDocuments(prev => [result.document, ...prev]);
      }
      
      return result;
    } catch (err) {
      setError(err.message || 'Failed to create document');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const editDocument = async (documentId, documentData) => {
    try {
      setLoading(true);
      const result = await updateDocument(documentId, documentData);
      
      // Update the document in the list
      if (result.document) {
        setDocuments(prev => 
          prev.map(doc => 
            doc.id === documentId ? result.document : doc
          )
        );
      }
      
      return result;
    } catch (err) {
      setError(err.message || 'Failed to update document');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const removeDocument = async (documentId) => {
    try {
      setLoading(true);
      await deleteDocument(documentId);
      
      // Remove the document from the list
      setDocuments(prev => prev.filter(doc => doc.id !== documentId));
      
      return { success: true };
    } catch (err) {
      setError(err.message || 'Failed to delete document');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const changePage = (page) => {
    setCurrentPage(page);
    fetchDocuments(page, filters);
  };

  const applyFilters = (newFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
    fetchDocuments(1, newFilters);
  };

  const value = {
    documents,
    loading,
    error,
    totalPages,
    currentPage,
    filters,
    fetchDocuments,
    addDocument,
    editDocument,
    removeDocument,
    changePage,
    applyFilters
  };

  return (
    <DocumentContext.Provider value={value}>
      {children}
    </DocumentContext.Provider>
  );
};

export default DocumentContext;