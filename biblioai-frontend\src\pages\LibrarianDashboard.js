import React, { useState, useEffect } from 'react';
import DocumentList from '../components/documents/DocumentList';
import DocumentForm from '../components/documents/DocumentForm';
import DocumentDetail from '../components/documents/DocumentDetail';
import Statistics from '../components/statistics/Statistics';
import { logout } from '../services/authService';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import '../styles/LibrarianDashboard.css';

const LibrarianDashboard = () => {
  // Theme and Language hooks
  const { getCurrentColors, toggleTheme, isDarkMode } = useTheme();
  const { t, changeLanguage, currentLanguage } = useLanguage();
  const colors = getCurrentColors();

  // Function to classify document using GROQ AI
  const handleClassifyDocument = async () => {
    try {
      const GROQ_API_KEY = '********************************************************';
      const promptTemplate = `Voici un document avec les informations suivantes :

Titre : ${documentToClassify.title}  
Description : ${documentToClassify.description}

Peux-tu suggérer :
- Une catégorie (ex : Science, Non-Fiction, etc.)
- Un sujet (ex : Informatique, Programmation, etc.)
- 3 à 5 mots-clés pertinents

Réponds dans un format JSON.
{
  "category": "Science",
  "subject": "Computer Science",
  "keywords": ["Python", "programmation", "débutants", "guide", "coding"]
}`;

      // Mock API call for development (in production, use actual fetch)
      // Normally this would be a server-side call to protect the API key
      console.log('Classifying document with GROQ AI...');
      console.log('API call would be:');
      console.log(`curl https://api.groq.com/openai/v1/chat/completions \\`);
      console.log(`-H "Content-Type: application/json" \\`);
      console.log(`-H "Authorization: Bearer ${GROQ_API_KEY}" \\`);
      console.log(`-d '{`);
      console.log(`"model": "llama3-70b-8192",`);
      console.log(`"messages": [{`);
      console.log(`    "role": "user",`);
      console.log(`    "content": "${promptTemplate.replace(/\n/g, '\\n')}"`);
      console.log(`}]`);
      console.log(`}'`);
     
      // Simulate classification result for demonstration
      setTimeout(() => {
        // Example result that would come from the API
        let resultExample;
        
        // Customize based on document
        if (documentToClassify.title.toLowerCase().includes('python')) {
          resultExample = {
            category: 'Science',
            subject: 'Computer Science',
            keywords: ['Python', 'programmation', 'débutants', 'guide', 'coding']
          };
        } else if (documentToClassify.title.toLowerCase().includes('histoire')) {
          resultExample = {
            category: 'Non-Fiction',
            subject: 'Histoire',
            keywords: ['histoire', 'civilisation', 'documentaire', 'recherche']
          };
        } else {
          // Default fallback
          resultExample = {
            category: 'Literature',
            subject: documentToClassify.title.includes('novel') ? 'Fiction' : 'General Knowledge',
            keywords: ['livre', 'information', 'connaissance', 'éducation']
          };
        }
        
        setClassificationResult(resultExample);
        setIsClassifying(false);
      }, 1500);
      
    } catch (error) {
      console.error('Error classifying document:', error);
      alert('Error classifying document. Please try again.');
    }
  };
  const [activeView, setActiveView] = useState('documents');
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [showForm, setShowForm] = useState(false);
  
  // States for document classification
  const [documentToClassify, setDocumentToClassify] = useState({ title: '', description: '' });
  const [isClassifying, setIsClassifying] = useState(false);
  const [classificationResult, setClassificationResult] = useState(null);
  const [showDetail, setShowDetail] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const navigate = useNavigate();

  const user = JSON.parse(localStorage.getItem('user') || '{}');

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleAddDocument = () => {
    setSelectedDocument(null);
    setShowForm(true);
  };

  const handleEditDocument = (document) => {
    setSelectedDocument(document);
    setShowForm(true);
    setShowDetail(false);
  };

  const handleViewDocument = (document) => {
    setSelectedDocument(document);
    setShowDetail(true);
  };

  const handleFormSave = (updatedDocument) => {
    console.log('DASHBOARD DEBUG: Form saved with document:', updatedDocument);
    setShowForm(false);
    setSelectedDocument(null);

    // Add a small delay to ensure backend processing is complete, then refresh
    setTimeout(() => {
      setRefreshTrigger(prev => {
        const newTrigger = prev + 1;
        console.log('DASHBOARD DEBUG: Triggering refresh with trigger:', newTrigger);
        return newTrigger;
      });
    }, 100); // 100ms delay
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setSelectedDocument(null);
  };

  const handleDetailClose = () => {
    setShowDetail(false);
    setSelectedDocument(null);
  };

  const renderContent = () => {
    switch (activeView) {
      case 'documents':
        return (
          <div className="documents-section">
            <div className="section-header">
              <h2>Document Management</h2>
              <button
                onClick={handleAddDocument}
                className="btn-primary add-document-btn"
              >
                + Add New Document
              </button>
            </div>
            <DocumentList
              onEdit={handleEditDocument}
              onView={handleViewDocument}
              refreshTrigger={refreshTrigger}
            />
          </div>
        );

      case 'analytics':
        return <Statistics />;

      default:
        return null;
    }
  };

  return (
    <div
      className="librarian-dashboard transition-colors duration-200"
      style={{ backgroundColor: colors.background, color: colors.text }}
    >
      {/* Header with theme and language controls */}
      <div
        className="dashboard-header flex justify-between items-center p-4 border-b transition-colors duration-200"
        style={{ borderBottomColor: colors.border, backgroundColor: colors.surface }}
      >
        <div className="flex items-center space-x-4">
          <h1
            className="text-2xl font-bold transition-colors duration-200"
            style={{ color: colors.text }}
          >
            {t('nav.dashboard') || 'Librarian Dashboard'}
          </h1>
        </div>

        <div className="flex items-center space-x-3">
          {/* Theme Toggle */}
          <button
            onClick={toggleTheme}
            className="p-2 rounded-lg transition-colors duration-200"
            style={{
              backgroundColor: `${colors.primary}20`,
              color: colors.text
            }}
            title={isDarkMode ? t('settings.lightMode') : t('settings.darkMode')}
          >
            {isDarkMode ? '☀️' : '🌙'}
          </button>

          {/* Language Toggle */}
          <button
            onClick={() => changeLanguage(currentLanguage === 'en' ? 'fr' : 'en')}
            className="p-2 rounded-lg transition-colors duration-200 text-sm font-medium"
            style={{
              backgroundColor: `${colors.primary}20`,
              color: colors.text
            }}
            title={t('settings.changeLanguage')}
          >
            {currentLanguage === 'en' ? '🇫🇷 FR' : '🇺🇸 EN'}
          </button>
        </div>
      </div>

      <div className="dashboard-container">
        <div
          className="dashboard-sidebar transition-colors duration-200"
          style={{ backgroundColor: colors.surface, borderRightColor: colors.border }}
        >
          <div className="user-info">
            <h3 style={{ color: colors.text }}>
              {t('dashboard.welcome') || 'Welcome'}, {user.username}
            </h3>
            <p
              className="user-role transition-colors duration-200"
              style={{ color: colors.textSecondary }}
            >
              {user.role}
            </p>
          </div>

          <nav className="dashboard-nav">
            <button
              className={`nav-item ${activeView === 'documents' ? 'active' : ''} transition-colors duration-200`}
              onClick={() => setActiveView('documents')}
              style={{
                backgroundColor: activeView === 'documents' ? colors.primary : 'transparent',
                color: activeView === 'documents' ? 'white' : colors.text,
                borderColor: colors.border
              }}
            >
              📚 {t('documents.management') || 'Document Management'}
            </button>
            <button
              className={`nav-item ${activeView === 'analytics' ? 'active' : ''} transition-colors duration-200`}
              onClick={() => setActiveView('analytics')}
              style={{
                backgroundColor: activeView === 'analytics' ? colors.primary : 'transparent',
                color: activeView === 'analytics' ? 'white' : colors.text,
                borderColor: colors.border
              }}
            >
              📊 {t('nav.analytics') || 'Analytics'}
            </button>
          </nav>

          <div className="sidebar-footer">
            <button
              className="logout-btn transition-colors duration-200"
              onClick={handleLogout}
              style={{
                backgroundColor: colors.error,
                color: 'white',
                borderColor: colors.error
              }}
            >
              🚪 {t('nav.logout') || 'Logout'}
            </button>
          </div>
        </div>

        <div
          className="dashboard-content transition-colors duration-200"
          style={{ backgroundColor: colors.background }}
        >
          {renderContent()}
        </div>
      </div>

      {/* Footer supprimé pour utilisation pleine page */}

      {/* Modals */}
      {showForm && (
        <DocumentForm
          document={selectedDocument}
          onSave={handleFormSave}
          onCancel={handleFormCancel}
        />
      )}

      {showDetail && (
        <DocumentDetail
          document={selectedDocument}
          onClose={handleDetailClose}
          onEdit={handleEditDocument}
        />
      )}
    </div>
  );
};

export default LibrarianDashboard;
