const translations = {
  // Common
  common: {
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    cancel: 'Cancel',
    confirm: 'Confirm',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    search: 'Search',
    searching: 'Searching...',
    filter: 'Filter',
    sort: 'Sort',
    refresh: 'Refresh',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    close: 'Close',
    open: 'Open',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    apply: 'Apply',
    reset: 'Reset',
    clear: 'Clear',
    clearFilters: 'Clear Filters',
    generate: 'Generate',
    generating: 'Generating...',
    saving: 'Saving...',
    tryAgain: 'Try Again',
    go: 'Go',
    view: 'View'
  },

  // Navigation
  nav: {
    home: 'Home',
    dashboard: 'Dashboard',
    books: 'Books',
    members: 'Members',
    reports: 'Reports',
    settings: 'Settings',
    profile: 'Profile',
    logout: 'Logout',
    login: 'Login',
    register: 'Register',
    features: 'Features',
    services: 'Services',
    reviews: 'Reviews',
    location: 'Location',
    analytics: 'Analytics'
  },

  // Authentication
  auth: {
    login: 'Login',
    register: 'Register',
    logout: 'Logout',
    username: 'Username',
    password: 'Password',
    email: 'Email',
    confirmPassword: 'Confirm Password',
    forgotPassword: 'Forgot Password?',
    rememberMe: 'Remember Me',
    loginSuccess: 'Login successful',
    loginError: 'Invalid credentials',
    registerSuccess: 'Registration successful',
    registerError: 'Registration failed',
    logoutSuccess: 'Logged out successfully',
    invalidCredentials: 'Invalid username or password',
    accountCreated: 'Account created successfully',
    passwordMismatch: 'Passwords do not match',
    emailRequired: 'Email is required',
    usernameRequired: 'Username is required',
    passwordRequired: 'Password is required',
    noAccount: "Don't have an account?",
    hasAccount: 'Already have an account?',
    registerTitle: 'Register for BiblioAI'
  },

  // Hero Section
  hero: {
    title1: 'Search & review',
    title2: 'your',
    title3: 'fav book',
    title4: 'effortlessly',
    description: 'Embark on a literary journey like never before with our revolutionary library application! Introducing a seamless experience that transcends traditional boundaries, where you can effortlessly search your favorite books.✨',
    startButton: 'Start now'
  },

  // Features Section
  features: {
    title: 'FEATURES',
    subtitle: 'What You Can Do?',
    searchBook: 'Search book',
    searchBookDesc: 'Effortlessly find your next read with our powerful and intuitive book search.',
    reviewBook: 'Review book',
    reviewBookDesc: 'Discover insightful critiques and share your thoughts on diverse literary masterpieces effortlessly.',
    wishlistBook: 'Wishlist book',
    wishlistBookDesc: 'Curate your literary dreams–wishlist books for future adventures and discoveries.'
  },

  // Services Section
  services: {
    title: 'SERVICES',
    subtitle: 'The Services for You',
    rent: 'Rent',
    favoriteBook: 'your favorite book',
    fairlyEasy: 'fairly easy on',
    description1: 'Viewing, rent, and organize your favorite books has never been easier. An integrated digital library rent that\'s simple to use, BiblioSmart lets you spend less time managing your work and more time actually doing it!',
    description2: 'Effortless rentals, personalized shelves—BiblioSmart transforms book management, enhancing your reading experience~'
  },

  // Quick Rentals Section
  quickRentals: {
    title: 'Quick Book Rentals:',
    dive: 'Dive',
    into: 'into',
    readingInstantly: 'Reading Instantly',
    description1: 'Discover instant literary delight. Access a vast library, borrow your favorite reads, and dive into captivating stories within minutes. Reading made quick and easy, just a click away!',
    description2: 'Unlock a world of stories effortlessly. Browse genres, choose, rent in minutes. Seamlessly manage your reading adventures with our intuitive platform~'
  },

  // Reviews Section
  reviews: {
    title: 'REVIEWS',
    subtitle: 'Reviews of Others'
  },

  // Location Section
  location: {
    title: 'LOCATION',
    subtitle: 'Our Library Location'
  },

  // Footer Section
  footer: {
    managedBy: 'Managed By',
    socialMedia: 'Social Media',
    slogan: 'Slogan',
    hashtag: '#RentFavBooks',
    copyright: '© 2024 BiblioSmart. All rights reserved.'
  },

  // Dashboard
  dashboard: {
    title: 'Dashboard',
    overview: 'Overview',
    statistics: 'Statistics',
    recentActivity: 'Recent Activity',
    quickActions: 'Quick Actions',
    totalBooks: 'Active Borrowings',
    availableBooks: 'Available Books',
    borrowedBooks: 'Borrowed Books',
    totalMembers: 'Total Members',
    activeMembers: 'Active Members',
    overdueBooks: 'Overdue Items',
    reservations: 'Active Reservations',
    notifications: 'Notifications',
    activeBorrowings: 'Active Borrowings',
    activeReservations: 'Active Reservations',
    overdueItems: 'Overdue Items',
    totalFines: 'Total Fines'
  },

  // Books
  books: {
    title: 'Books',
    addBook: 'Add Book',
    editBook: 'Edit Book',
    deleteBook: 'Delete Book',
    bookTitle: 'Book Title',
    author: 'Author',
    isbn: 'ISBN',
    category: 'Category',
    subject: 'Subject',
    publisher: 'Publisher',
    publishedDate: 'Published Date',
    pages: 'Pages',
    language: 'Language',
    description: 'Description',
    totalCopies: 'Total Copies',
    availableCopies: 'Available Copies',
    location: 'Location',
    status: 'Status',
    available: 'Available',
    borrowed: 'Borrowed',
    reserved: 'Reserved',
    maintenance: 'Maintenance',
    searchBooks: 'Search Books',
    searchAndReserve: 'Search & Reserve Books',
    filterByCategory: 'Filter by Category',
    sortBy: 'Sort By',
    bookAdded: 'Book added successfully',
    bookUpdated: 'Book updated successfully',
    bookDeleted: 'Book deleted successfully',
    bookNotFound: 'Book not found',
    noBooks: 'No books found'
  },

  // Documents
  documents: {
    title: 'Documents',
    addDocument: 'Add Document',
    editDocument: 'Edit Document',
    updateDocument: 'Update Document',
    deleteDocument: 'Delete Document',
    library: 'Document Library',
    management: 'Document Management',
    searchPlaceholder: 'Search documents...',
    allTypes: 'All Types',
    books: 'Books',
    periodicals: 'Periodicals',
    articles: 'Articles',
    videos: 'Videos',
    by: 'by',
    noDescription: 'No description available',
    copies: 'Copies',
    available: 'Available',
    unavailable: 'Unavailable',
    category: 'Category',
    publisher: 'Publisher',
    noDocuments: 'No documents found',
    tryAdjustingCriteria: 'Try adjusting your search criteria.',
    showOnlyAvailable: 'Show only available documents',
    categoryPlaceholder: 'Category...',
    // Form fields
    title: 'Title',
    description: 'Description',
    documentType: 'Document Type',
    barcode: 'Barcode',
    basicInformation: 'Basic Information',
    // Detail view
    quickInformation: 'Quick Information',
    type: 'Type',
    copies: 'Copies',
    noImageAvailable: 'No Image Available',
    reserveDocument: 'Reserve Document',
    // Form sections
    classification: 'Classification',
    keywords: 'Keywords',
    subject: 'Subject',
    bookInformation: 'Book Information',
    periodicalInformation: 'Periodical Information',
    articleInformation: 'Article Information',
    videoInformation: 'Video Information',
    author: 'Author',
    edition: 'Edition',
    series: 'Series',
    frequency: 'Frequency',
    volume: 'Volume',
    issue: 'Issue',
    journal: 'Journal',
    director: 'Director',
    duration: 'Duration',
    resolution: 'Resolution',
    publicationDetails: 'Publication Details',
    publicationDate: 'Publication Date',
    language: 'Language',
    physicalDetails: 'Physical Details',
    pages: 'Pages',
    format: 'Format',
    shelfLocation: 'Shelf Location',
    availability: 'Availability',
    totalCopies: 'Total Copies',
    availableCopies: 'Available Copies',
    image: 'Image',
    documentImage: 'Document Image',
    // Detail view additional
    location: 'Location',
    metadata: 'Metadata',
    added: 'Added',
    lastUpdated: 'Last Updated',
    addedBy: 'Added by'
  },

  // Pagination
  pagination: {
    first: 'First',
    previous: 'Previous',
    next: 'Next',
    last: 'Last',
    page: 'Page',
    of: 'of',
    showing: 'Showing',
    goToPage: 'Go to page'
  },

  // Statistics
  statistics: {
    title: 'Library Statistics',
    overview: 'Overview',
    keyMetrics: 'Key Metrics',
    lastUpdated: 'Last updated',
    loadError: 'Failed to Load Statistics',
    noData: 'No Statistics Available',
    noDataMessage: 'Unable to generate statistics at this time.',
    documentTypes: 'Document Types',
    noDocumentTypeData: 'No document type data available'
  },

  // Members
  members: {
    title: 'Members',
    addMember: 'Add Member',
    editMember: 'Edit Member',
    deleteMember: 'Delete Member',
    memberName: 'Member Name',
    memberType: 'Member Type',
    studentId: 'Student ID',
    phone: 'Phone',
    address: 'Address',
    joinDate: 'Join Date',
    status: 'Status',
    active: 'Active',
    inactive: 'Inactive',
    suspended: 'Suspended',
    borrowingHistory: 'Borrowing History',
    currentBorrowings: 'Current Borrowings',
    reservations: 'Reservations',
    fines: 'Fines',
    memberAdded: 'Member added successfully',
    memberUpdated: 'Member updated successfully',
    memberDeleted: 'Member deleted successfully',
    memberNotFound: 'Member not found',
    noMembers: 'No members found'
  },

  // Borrowing
  borrowing: {
    title: 'Borrowing',
    borrowBook: 'Borrow Book',
    returnBook: 'Return Book',
    renewBook: 'Renew Book',
    borrowDate: 'Borrow Date',
    dueDate: 'Due Date',
    returnDate: 'Return Date',
    renewalCount: 'Renewal Count',
    fine: 'Fine',
    overdue: 'Overdue',
    returned: 'Returned',
    borrowed: 'Borrowed',
    myBorrowings: 'My Borrowings',
    borrowingHistory: 'Borrowing History',
    bookBorrowed: 'Book borrowed successfully',
    bookReturned: 'Book returned successfully',
    bookRenewed: 'Book renewed successfully',
    borrowingFailed: 'Failed to borrow book',
    returnFailed: 'Failed to return book',
    renewalFailed: 'Failed to renew book',
    maxRenewalsReached: 'Maximum renewals reached',
    bookOverdue: 'Book is overdue',
    noBorrowings: 'No borrowings found'
  },

  // Reservations
  reservations: {
    title: 'Reservations',
    reserveBook: 'Reserve Book',
    cancelReservation: 'Cancel Reservation',
    reservationDate: 'Reservation Date',
    expiryDate: 'Expiry Date',
    status: 'Status',
    pending: 'Pending',
    ready: 'Ready',
    expired: 'Expired',
    cancelled: 'Cancelled',
    myReservations: 'My Reservations',
    bookReserved: 'Book reserved successfully',
    reservationCancelled: 'Reservation cancelled successfully',
    reservationFailed: 'Failed to reserve book',
    reservationExpired: 'Reservation has expired',
    noReservations: 'No reservations found'
  },

  // AI Features
  ai: {
    title: 'AI Features',
    recommendations: 'AI Recommendations',
    chatAssistant: 'AI Chat Assistant',
    smartSearch: 'Smart Search',
    trending: 'Trending Books',
    forYou: 'For You',
    basedOnHistory: 'Based on your reading history and preferences',
    popularBooks: 'Most popular books in the last 30 days',
    whyRecommended: 'Why recommended:',
    matchesCategory: 'Matches your favorite category: {{category}}',
    matchesSubject: 'Matches your interest in: {{subject}}',
    favoriteAuthor: 'By your favorite author: {{author}}',
    aiAssistantWelcome: "Hello! I'm your AI library assistant. I can help you find books, answer questions about library policies, and assist with your borrowings and reservations. How can I help you today?",
    quickActions: 'Quick actions:',
    findProgrammingBooks: 'Find books about programming',
    checkBorrowings: 'Check my current borrowings',
    renewBook: 'How do I renew a book?',
    libraryPolicies: 'What are the library policies?',
    recommendBooks: 'Recommend books for me',
    thinking: 'Thinking...',
    typeMessage: 'Ask me anything about the library...',
    noRecommendations: 'Start borrowing books to get personalized recommendations!',
    noTrendingData: 'No trending data available yet.',
    aiError: 'Sorry, I encountered an error. Please try again.',
    connectionError: "Sorry, I'm having trouble connecting right now. Please try again later."
  },

  // Settings
  settings: {
    title: 'Settings',
    general: 'General',
    appearance: 'Appearance',
    language: 'Language',
    notifications: 'Notifications',
    privacy: 'Privacy',
    security: 'Security',
    account: 'Account',
    darkMode: 'Dark Mode',
    lightMode: 'Light Mode',
    theme: 'Theme',
    changeLanguage: 'Change Language',
    selectLanguage: 'Select Language',
    emailNotifications: 'Email Notifications',
    pushNotifications: 'Push Notifications',
    smsNotifications: 'SMS Notifications',
    changePassword: 'Change Password',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmNewPassword: 'Confirm New Password',
    passwordChanged: 'Password changed successfully',
    settingsSaved: 'Settings saved successfully'
  },

  // Notifications
  notifications: {
    title: 'Notifications',
    markAsRead: 'Mark as Read',
    markAllAsRead: 'Mark All as Read',
    deleteNotification: 'Delete Notification',
    noNotifications: 'No notifications',
    bookDueSoon: 'Book due soon: {{title}}',
    bookOverdue: 'Book overdue: {{title}}',
    bookReady: 'Reserved book ready: {{title}}',
    reservationExpiring: 'Reservation expiring: {{title}}',
    newBookAvailable: 'New book available: {{title}}',
    accountSuspended: 'Your account has been suspended',
    fineAdded: 'Fine added: ${{amount}}',
    reminderDue: 'Reminder: Book due tomorrow',
    notificationRead: 'Notification marked as read',
    allNotificationsRead: 'All notifications marked as read'
  },

  // Errors
  errors: {
    general: 'An error occurred',
    networkError: 'Network error. Please check your connection.',
    serverError: 'Server error. Please try again later.',
    notFound: 'Resource not found',
    unauthorized: 'You are not authorized to perform this action',
    forbidden: 'Access forbidden',
    validationError: 'Please check your input',
    sessionExpired: 'Your session has expired. Please login again.',
    fileUploadError: 'Failed to upload file',
    fileSizeError: 'File size too large',
    fileTypeError: 'Invalid file type'
  },

  // Success Messages
  success: {
    operationCompleted: 'Operation completed successfully',
    dataSaved: 'Data saved successfully',
    dataUpdated: 'Data updated successfully',
    dataDeleted: 'Data deleted successfully',
    emailSent: 'Email sent successfully',
    passwordReset: 'Password reset successfully',
    profileUpdated: 'Profile updated successfully',
    settingsUpdated: 'Settings updated successfully'
  }
};

export default translations;
