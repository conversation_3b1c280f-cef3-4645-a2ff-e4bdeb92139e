import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';

const ThemeLanguageSettings = () => {
  const { isDarkMode, toggleTheme, getCurrentColors } = useTheme();
  const { currentLanguage, changeLanguage, t, supportedLanguages, isLoading } = useLanguage();
  const colors = getCurrentColors();

  const handleLanguageChange = (langCode) => {
    changeLanguage(langCode);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">{t('common.loading')}</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Theme Settings */}
      <Card 
        className="transition-colors duration-200"
        style={{ 
          backgroundColor: colors.surface,
          borderColor: colors.border,
          color: colors.text
        }}
      >
        <CardHeader>
          <CardTitle style={{ color: colors.text }}>
            🎨 {t('settings.appearance')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium" style={{ color: colors.text }}>
                  {t('settings.theme')}
                </h3>
                <p className="text-sm" style={{ color: colors.textSecondary }}>
                  {isDarkMode ? t('settings.darkMode') : t('settings.lightMode')}
                </p>
              </div>
              <Button
                onClick={toggleTheme}
                variant={isDarkMode ? 'default' : 'outline'}
                className="flex items-center space-x-2"
                style={{
                  backgroundColor: isDarkMode ? colors.primary : 'transparent',
                  borderColor: colors.border,
                  color: isDarkMode ? 'white' : colors.text
                }}
              >
                <span>{isDarkMode ? '🌙' : '☀️'}</span>
                <span>{isDarkMode ? t('settings.darkMode') : t('settings.lightMode')}</span>
              </Button>
            </div>

            {/* Theme Preview */}
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div 
                className="p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:scale-105"
                style={{ 
                  backgroundColor: !isDarkMode ? colors.primary : colors.surface,
                  borderColor: !isDarkMode ? colors.primary : colors.border,
                  opacity: !isDarkMode ? 1 : 0.7
                }}
                onClick={() => !isDarkMode || toggleTheme()}
              >
                <div className="flex items-center space-x-2">
                  <span>☀️</span>
                  <span style={{ color: !isDarkMode ? 'white' : colors.text }}>
                    {t('settings.lightMode')}
                  </span>
                </div>
                <div className="mt-2 space-y-1">
                  <div 
                    className="h-2 rounded"
                    style={{ backgroundColor: !isDarkMode ? 'rgba(255,255,255,0.8)' : colors.border }}
                  ></div>
                  <div 
                    className="h-2 rounded w-3/4"
                    style={{ backgroundColor: !isDarkMode ? 'rgba(255,255,255,0.6)' : colors.border }}
                  ></div>
                </div>
              </div>

              <div 
                className="p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:scale-105"
                style={{ 
                  backgroundColor: isDarkMode ? colors.primary : colors.surface,
                  borderColor: isDarkMode ? colors.primary : colors.border,
                  opacity: isDarkMode ? 1 : 0.7
                }}
                onClick={() => isDarkMode || toggleTheme()}
              >
                <div className="flex items-center space-x-2">
                  <span>🌙</span>
                  <span style={{ color: isDarkMode ? 'white' : colors.text }}>
                    {t('settings.darkMode')}
                  </span>
                </div>
                <div className="mt-2 space-y-1">
                  <div 
                    className="h-2 rounded"
                    style={{ backgroundColor: isDarkMode ? 'rgba(255,255,255,0.8)' : colors.border }}
                  ></div>
                  <div 
                    className="h-2 rounded w-3/4"
                    style={{ backgroundColor: isDarkMode ? 'rgba(255,255,255,0.6)' : colors.border }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Language Settings */}
      <Card 
        className="transition-colors duration-200"
        style={{ 
          backgroundColor: colors.surface,
          borderColor: colors.border,
          color: colors.text
        }}
      >
        <CardHeader>
          <CardTitle style={{ color: colors.text }}>
            🌍 {t('settings.language')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium mb-2" style={{ color: colors.text }}>
                {t('settings.selectLanguage')}
              </h3>
              <p className="text-sm mb-4" style={{ color: colors.textSecondary }}>
                {t('settings.changeLanguage')}
              </p>
            </div>

            {/* Language Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {Object.values(supportedLanguages).map((language) => (
                <div
                  key={language.code}
                  className="p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:scale-105"
                  style={{
                    backgroundColor: currentLanguage === language.code ? colors.primary : colors.background,
                    borderColor: currentLanguage === language.code ? colors.primary : colors.border,
                    color: currentLanguage === language.code ? 'white' : colors.text
                  }}
                  onClick={() => handleLanguageChange(language.code)}
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{language.flag}</span>
                    <div>
                      <div className="font-medium text-sm">{language.name}</div>
                      <div 
                        className="text-xs"
                        style={{ 
                          color: currentLanguage === language.code ? 'rgba(255,255,255,0.8)' : colors.textSecondary 
                        }}
                      >
                        {language.code.toUpperCase()}
                      </div>
                    </div>
                  </div>
                  {currentLanguage === language.code && (
                    <div className="mt-2 flex justify-end">
                      <span className="text-xs">✓</span>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Current Language Info */}
            <div 
              className="p-3 rounded-lg"
              style={{ backgroundColor: colors.background, borderColor: colors.border }}
            >
              <div className="flex items-center space-x-2">
                <span className="text-lg">{supportedLanguages[currentLanguage].flag}</span>
                <div>
                  <div className="font-medium" style={{ color: colors.text }}>
                    {t('settings.language')}: {supportedLanguages[currentLanguage].name}
                  </div>
                  <div className="text-sm" style={{ color: colors.textSecondary }}>
                    {supportedLanguages[currentLanguage].direction === 'rtl' ? 'Right-to-Left' : 'Left-to-Right'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card 
        className="transition-colors duration-200"
        style={{ 
          backgroundColor: colors.surface,
          borderColor: colors.border,
          color: colors.text
        }}
      >
        <CardHeader>
          <CardTitle style={{ color: colors.text }}>
            ⚡ {t('dashboard.quickActions')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <Button
              onClick={toggleTheme}
              variant="outline"
              className="flex items-center space-x-2"
              style={{
                borderColor: colors.border,
                color: colors.text,
                backgroundColor: 'transparent'
              }}
            >
              <span>{isDarkMode ? '☀️' : '🌙'}</span>
              <span>{isDarkMode ? t('settings.lightMode') : t('settings.darkMode')}</span>
            </Button>

            <Button
              onClick={() => handleLanguageChange(currentLanguage === 'en' ? 'fr' : 'en')}
              variant="outline"
              className="flex items-center space-x-2"
              style={{
                borderColor: colors.border,
                color: colors.text,
                backgroundColor: 'transparent'
              }}
            >
              <span>🌍</span>
              <span>
                {currentLanguage === 'en' ? 'Français' : 'English'}
              </span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ThemeLanguageSettings;
