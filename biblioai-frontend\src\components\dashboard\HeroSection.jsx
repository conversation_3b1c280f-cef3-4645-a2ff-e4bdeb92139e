// src/components/common/HeroSection.jsx
import React from 'react';
import { Link } from 'react-router-dom';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

const HeroSection = () => {
  const { getCurrentColors } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();

  return (
    <section
      className="relative pt-4 pb-32 px-4 md:px-8 transition-colors duration-200"
      style={{ backgroundColor: colors.background }}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 items-center">
        <div className="max-w-xl">
          <h1
            className="text-5xl md:text-6xl lg:text-7xl font-extrabold leading-tight transition-colors duration-200"
            style={{ color: colors.text }}
          >
            {t('hero.title1') || 'Search & review'}<br />
            {t('hero.title2') || 'your'} <span
              className="underline"
              style={{ color: colors.primary }}
            >
              {t('hero.title3') || 'fav book'}
            </span> {t('hero.title4') || 'effortlessly'}
          </h1>

          <p
            className="mt-8 text-base leading-relaxed transition-colors duration-200"
            style={{ color: colors.textSecondary }}
          >
            {t('hero.description') || 'Embark on a literary journey like never before with our revolutionary library application! Introducing a seamless experience that transcends traditional boundaries, where you can effortlessly search your favorite books.✨'}
          </p>

          <Link to="/register">
            <button
              className="mt-10 text-white font-bold py-5 px-14 rounded-[10px] shadow-[0_30px_60px_rgba(68,117,242,0.25)] transition-colors duration-300"
              style={{ backgroundColor: colors.primary }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#3461d1'}
              onMouseLeave={(e) => e.target.style.backgroundColor = colors.primary}
            >
              {t('hero.startButton') || 'Start now'} →
            </button>
          </Link>
        </div>
        
        <div className="relative hidden lg:flex justify-center items-center h-[720px] overflow-hidden lg:col-start-2">
  {/* Background shape container with proper responsive sizing */}
  <div className="absolute inset-0 flex justify-center items-center">
    <img 
      src="/images/img_cover_indigo_a200.svg" 
      alt="Background shape" 
      className="w-full max-w-[713px] h-full max-h-[720px] object-contain" 
    />
  </div>
  
  {/* Books container with smaller images and increased spacing */}
  <div className="relative w-full max-w-[678px] h-full">
    <img 
      src="/images/img_talking_to_strangers.png" 
      alt="Talking to Strangers book" 
      className="absolute top-[15%] left-[45%] w-[120px] max-w-[18%] rounded-[10px] shadow-lg transform hover:scale-105 transition-transform duration-300" 
    />
    
    <img 
      src="/images/img_dompet_ayah_sepatu_ibu.png" 
      alt="Dompet Ayah Sepatu Ibu book" 
      className="absolute top-[50%] left-[8%] w-[140px] max-w-[21%] rounded-[10px] shadow-lg transform hover:scale-105 transition-transform duration-300" 
    />
    
    <img 
      src="/images/img_the_midnight_library.png" 
      alt="The Midnight Library book" 
      className="absolute top-[25%] right-[12%] w-[110px] max-w-[16%] rounded-[10px] shadow-lg transform hover:scale-105 transition-transform duration-300" 
    />
    
    <img 
      src="/images/img_the_visual_mba.png" 
      alt="The Visual MBA book" 
      className="absolute bottom-[15%] left-[55%] w-[100px] max-w-[15%] rounded-[10px] shadow-lg transform hover:scale-105 transition-transform duration-300" 
    />
  </div>
</div>

      </div>
    </section>
  );
};

export default HeroSection;