import axios from 'axios';

// API Gateway URL
const API_GATEWAY_URL = 'http://localhost:5000/api';

// Direct service URLs for fallback
const DOCUMENT_SERVICE_URL = 'http://localhost:5001';
const SEARCH_SERVICE_URL = 'http://localhost:5004';

// Create axios instance with auth header
const createAuthHeader = () => {
  const token = localStorage.getItem('token');
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// Search documents using the dedicated search service
export const searchDocuments = async (params = {}) => {
  try {
    // Map frontend parameters to search service parameters
    const searchParams = {};
    
    if (params.search) {
      searchParams.q = params.search;
    }
    
    if (params.type) {
      searchParams.type = params.type;
    }
    
    if (params.category) {
      searchParams.category = params.category;
    }
    
    if (params.available_only !== undefined) {
      searchParams.available_only = params.available_only;
    }
    
    if (params.page) {
      searchParams.page = params.page;
    }
    
    if (params.per_page) {
      searchParams.per_page = params.per_page;
    }

    console.log('[SEARCH SERVICE] Sending search request with params:', searchParams);
    
    // Try API Gateway first
    try {
      const response = await axios.get(`${API_GATEWAY_URL}/search`, {
        headers: createAuthHeader(),
        params: searchParams
      });
      
      console.log('[SEARCH SERVICE] Search response:', response.data);
      return response.data;
    } catch (gatewayError) {
      console.log('[SEARCH SERVICE] API Gateway failed, trying direct search service', gatewayError.message);
      // If API Gateway fails, try direct service connection
      // Note: Search service doesn't have /api prefix
      const response = await axios.get(`${SEARCH_SERVICE_URL}/search`, {
        headers: createAuthHeader(),
        params: searchParams
      });
      
      console.log('[SEARCH SERVICE] Direct search response:', response.data);
      return response.data;
    }
  } catch (error) {
    console.error('[SEARCH SERVICE] Search error:', error);
    throw error.response?.data || { message: 'Search service error' };
  }
};

// Get search suggestions
export const getSearchSuggestions = async (query) => {
  try {
    const response = await axios.get(`${API_GATEWAY_URL}/search/suggestions`, {
      headers: createAuthHeader(),
      params: { q: query }
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Reindex all documents for search
export const reindexDocuments = async () => {
  try {
    const response = await axios.post(`${API_GATEWAY_URL}/search/reindex`, {}, {
      headers: createAuthHeader()
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || { message: 'Server error' };
  }
};

// Fallback to document service if search service fails
export const fallbackGetDocuments = async (params = {}) => {
  try {
    console.log('[SEARCH SERVICE] Using fallback document service directly');
    // Try API Gateway first
    try {
      const response = await axios.get(`${API_GATEWAY_URL}/documents`, {
        headers: createAuthHeader(),
        params
      });
      console.log('[SEARCH SERVICE] API Gateway documents response:', response.data);
      return response.data;
    } catch (gatewayError) {
      console.log('[SEARCH SERVICE] API Gateway failed, trying direct document service', gatewayError.message);
      // If API Gateway fails, try direct service connection
      // Note: Document service doesn't have /api prefix
      const response = await axios.get(`${DOCUMENT_SERVICE_URL}/documents`, {
        headers: createAuthHeader(),
        params
      });
      console.log('[SEARCH SERVICE] Direct document service response:', response.data);
      return response.data;
    }
  } catch (error) {
    console.error('[SEARCH SERVICE] All document service attempts failed:', error.message);
    throw error.response?.data || { message: 'Document service error' };
  }
};

// Smart search function that tries search service first, then falls back to document service
export const smartSearch = async (params = {}) => {
  try {
    // If there's a search term, use the search service
    if (params.search && params.search.trim()) {
      try {
        console.log('[SEARCH SERVICE] Using search service for query:', params.search);
        return await searchDocuments(params);
      } catch (searchError) {
        console.warn('[SEARCH SERVICE] Search service failed, falling back to document service:', searchError.message);
        // If search service fails, fall back to document service
        return await fallbackGetDocuments(params);
      }
    } else {
      // For browsing without search, use document service
      console.log('[SEARCH SERVICE] Using document service for browsing');
      try {
        // Try with API Gateway first
        const response = await axios.get(`${API_GATEWAY_URL}/documents`, {
          headers: createAuthHeader(),
          params
        });
        console.log('[SEARCH SERVICE] API Gateway documents response:', response.data);
        return response.data;
      } catch (gatewayError) {
        console.log('[SEARCH SERVICE] API Gateway failed, trying direct document service');
        // If API Gateway fails, try direct service connection
        try {
          const response = await axios.get(`${DOCUMENT_SERVICE_URL}/documents`, {
            headers: createAuthHeader(),
            params
          });
          console.log('[SEARCH SERVICE] Direct document service response:', response.data);
          return response.data;
        } catch (directError) {
          // If direct service also fails, throw a comprehensive error
          console.error('[SEARCH SERVICE] All document service attempts failed');
          throw new Error('Failed to fetch documents from both API gateway and direct service');
        }
      }
    }
  } catch (error) {
    console.error('[SEARCH SERVICE] Critical error in smart search:', error.message);
    // Rethrow to be handled by the component
    throw error;
  }
};
