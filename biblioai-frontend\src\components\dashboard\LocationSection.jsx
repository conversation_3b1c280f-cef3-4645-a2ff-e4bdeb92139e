import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

const LocationSection = () => {
  // Theme and Language hooks
  const { getCurrentColors } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();
  return (
    <section id="location" className="py-16 px-4 md:px-8 lg:px-20 transition-colors duration-200">
      <div className="mb-12">
        <span
          className="text-lg font-extrabold tracking-wider transition-colors duration-200"
          style={{ color: colors.primary }}
        >
          {t('location.title') || 'LOCATION'}
        </span>
        <h2
          className="text-4xl font-extrabold mt-2 transition-colors duration-200"
          style={{ color: colors.text }}
        >
          🗺• {t('location.subtitle') || 'Our Library Location'}
        </h2>
      </div>
      
      <div className="mt-10">
        <img 
          src="/images/img_location.png" 
          alt="Library location map" 
          className="w-full max-w-[1200px] h-auto rounded-[10px]" 
        />
      </div>
    </section>
  );
};

export default LocationSection;