import React, { useState, useEffect } from 'react';
import statisticsService from '../../services/statisticsService';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import './Statistics.css';

const Statistics = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [lastUpdated, setLastUpdated] = useState(null);

  // Theme and Language hooks
  const { getCurrentColors } = useTheme();
  const { t } = useLanguage();
  const colors = getCurrentColors();

  const fetchStatistics = async () => {
    try {
      setLoading(true);
      setError('');
      
      console.log('STATISTICS: Fetching data...');
      const rawStats = await statisticsService.getAllStatistics();
      const formattedStats = statisticsService.formatStatistics(rawStats);
      
      setStats(formattedStats);
      setLastUpdated(new Date());
      console.log('STATISTICS: Data loaded successfully');
      
    } catch (err) {
      console.error('STATISTICS: Error loading data:', err);
      setError(err.message || 'Failed to load statistics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatistics();
  }, []);

  const handleRefresh = () => {
    fetchStatistics();
  };

  if (loading) {
    return (
      <div
        className="statistics-container transition-colors duration-200"
        style={{ backgroundColor: colors.background, color: colors.text }}
      >
        <div
          className="statistics-header transition-colors duration-200"
          style={{ backgroundColor: colors.surface, borderBottomColor: colors.border }}
        >
          <h2 style={{ color: colors.text }}>
            📊 {t('statistics.title') || 'Library Statistics'}
          </h2>
        </div>
        <div
          className="loading-state transition-colors duration-200"
          style={{ color: colors.textSecondary }}
        >
          <div className="loading-spinner"></div>
          <p>{t('common.loading') || 'Loading statistics...'}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="statistics-container transition-colors duration-200"
        style={{ backgroundColor: colors.background, color: colors.text }}
      >
        <div
          className="statistics-header transition-colors duration-200"
          style={{ backgroundColor: colors.surface, borderBottomColor: colors.border }}
        >
          <h2 style={{ color: colors.text }}>
            📊 {t('statistics.title') || 'Library Statistics'}
          </h2>
        </div>
        <div
          className="error-state transition-colors duration-200"
          style={{ color: colors.text }}
        >
          <div className="error-icon">⚠️</div>
          <h3 style={{ color: colors.error }}>
            {t('statistics.loadError') || 'Failed to Load Statistics'}
          </h3>
          <p style={{ color: colors.textSecondary }}>{error}</p>
          <button
            onClick={handleRefresh}
            className="retry-btn transition-colors duration-200"
            style={{ backgroundColor: colors.primary, color: 'white' }}
          >
            🔄 {t('common.tryAgain') || 'Try Again'}
          </button>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div
        className="statistics-container transition-colors duration-200"
        style={{ backgroundColor: colors.background, color: colors.text }}
      >
        <div
          className="statistics-header transition-colors duration-200"
          style={{ backgroundColor: colors.surface, borderBottomColor: colors.border }}
        >
          <h2 style={{ color: colors.text }}>
            📊 {t('statistics.title') || 'Library Statistics'}
          </h2>
        </div>
        <div
          className="no-data-state transition-colors duration-200"
          style={{ color: colors.textSecondary }}
        >
          <div className="no-data-icon">📊</div>
          <h3 style={{ color: colors.text }}>
            {t('statistics.noData') || 'No Statistics Available'}
          </h3>
          <p>{t('statistics.noDataMessage') || 'Unable to generate statistics at this time.'}</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="statistics-container transition-colors duration-200"
      style={{ backgroundColor: colors.background, color: colors.text }}
    >
      {/* Header */}
      <div
        className="statistics-header transition-colors duration-200"
        style={{ backgroundColor: colors.surface, borderBottomColor: colors.border }}
      >
        <div className="header-left">
          <h2 style={{ color: colors.text }}>
            📊 {t('statistics.title') || 'Library Statistics'}
          </h2>
          {lastUpdated && (
            <p
              className="last-updated transition-colors duration-200"
              style={{ color: colors.textSecondary }}
            >
              {t('statistics.lastUpdated') || 'Last updated'}: {lastUpdated.toLocaleString()}
            </p>
          )}
        </div>
        <button
          onClick={handleRefresh}
          className="refresh-btn transition-colors duration-200"
          disabled={loading}
          style={{ backgroundColor: colors.primary, color: 'white' }}
        >
          🔄 {t('common.refresh') || 'Refresh'}
        </button>
      </div>

      {/* Overview Cards */}
      <div className="overview-section">
        <h3 style={{ color: colors.text }}>
          {t('statistics.overview') || 'Overview'}
        </h3>
        <div className="overview-grid">
          {stats.overview.map((item, index) => (
            <div
              key={index}
              className={`overview-card ${item.color} transition-colors duration-200`}
              style={{
                backgroundColor: colors.surface,
                borderColor: colors.border,
                boxShadow: `0 2px 8px ${colors.shadow}`
              }}
            >
              <div className="card-icon">{item.icon}</div>
              <div className="card-content">
                <h4 style={{ color: colors.text }}>{item.title}</h4>
                <div
                  className="card-value transition-colors duration-200"
                  style={{ color: colors.primary }}
                >
                  {item.value}
                </div>
                <p
                  className="card-subtitle transition-colors duration-200"
                  style={{ color: colors.textSecondary }}
                >
                  {item.subtitle}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Secondary Metrics */}
      <div className="metrics-section">
        <h3 style={{ color: colors.text }}>
          {t('statistics.keyMetrics') || 'Key Metrics'}
        </h3>
        <div className="metrics-grid">
          {stats.metrics.map((metric, index) => (
            <div
              key={index}
              className="metric-card transition-colors duration-200"
              style={{
                backgroundColor: colors.surface,
                borderColor: colors.border,
                boxShadow: `0 2px 8px ${colors.shadow}`
              }}
            >
              <h4 style={{ color: colors.text }}>{metric.title}</h4>
              <div
                className="metric-value transition-colors duration-200"
                style={{ color: colors.primary }}
              >
                {metric.value}
              </div>
              <p
                className="metric-subtitle transition-colors duration-200"
                style={{ color: colors.textSecondary }}
              >
                {metric.subtitle}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Charts Section */}
      <div className="charts-section">
        <div className="charts-grid">
          {/* Document Types */}
          <div
            className="chart-card transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border,
              boxShadow: `0 2px 8px ${colors.shadow}`
            }}
          >
            <h4 style={{ color: colors.text }}>
              📚 {t('statistics.documentTypes') || 'Document Types'}
            </h4>
            <div className="chart-content">
              {stats.charts.documentTypes.length > 0 ? (
                <div className="bar-chart">
                  {stats.charts.documentTypes.map((item, index) => (
                    <div key={index} className="bar-item">
                      <div
                        className="bar-label transition-colors duration-200"
                        style={{ color: colors.text }}
                      >
                        {item.name}
                      </div>
                      <div className="bar-container">
                        <div
                          className="bar-fill"
                          style={{
                            width: `${(item.value / Math.max(...stats.charts.documentTypes.map(d => d.value))) * 100}%`,
                            backgroundColor: colors.primary
                          }}
                        ></div>
                        <span
                          className="bar-value transition-colors duration-200"
                          style={{ color: colors.text }}
                        >
                          {item.value}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p
                  className="no-data transition-colors duration-200"
                  style={{ color: colors.textSecondary }}
                >
                  {t('statistics.noDocumentTypeData') || 'No document type data available'}
                </p>
              )}
            </div>
          </div>

          {/* Top Categories */}
          <div
            className="chart-card transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border,
              boxShadow: `0 2px 8px ${colors.shadow}`
            }}
          >
            <h4 style={{ color: colors.text }}>🏷️ Top Categories</h4>
            <div className="chart-content">
              {stats.charts.categories.length > 0 ? (
                <div className="list-chart">
                  {stats.charts.categories.slice(0, 8).map((item, index) => (
                    <div key={index} className="list-item">
                      <span
                        className="list-rank transition-colors duration-200"
                        style={{ color: colors.primary }}
                      >
                        #{index + 1}
                      </span>
                      <span
                        className="list-name transition-colors duration-200"
                        style={{ color: colors.text }}
                      >
                        {item.name}
                      </span>
                      <span
                        className="list-value transition-colors duration-200"
                        style={{ color: colors.textSecondary }}
                      >
                        {item.value}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <p
                  className="no-data transition-colors duration-200"
                  style={{ color: colors.textSecondary }}
                >
                  No category data available
                </p>
              )}
            </div>
          </div>

          {/* Languages */}
          <div
            className="chart-card transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border,
              boxShadow: `0 2px 8px ${colors.shadow}`
            }}
          >
            <h4 style={{ color: colors.text }}>🌍 Languages</h4>
            <div className="chart-content">
              {stats.charts.languages.length > 0 ? (
                <div className="pie-chart-list">
                  {stats.charts.languages.map((item, index) => (
                    <div key={index} className="pie-item">
                      <div className="pie-color" style={{ backgroundColor: `hsl(${index * 60}, 70%, 60%)` }}></div>
                      <span
                        className="pie-label transition-colors duration-200"
                        style={{ color: colors.text }}
                      >
                        {item.name}
                      </span>
                      <span
                        className="pie-value transition-colors duration-200"
                        style={{ color: colors.textSecondary }}
                      >
                        {item.value}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <p
                  className="no-data transition-colors duration-200"
                  style={{ color: colors.textSecondary }}
                >
                  No language data available
                </p>
              )}
            </div>
          </div>

          {/* Top Publishers */}
          <div
            className="chart-card transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border,
              boxShadow: `0 2px 8px ${colors.shadow}`
            }}
          >
            <h4 style={{ color: colors.text }}>🏢 Top Publishers</h4>
            <div className="chart-content">
              {stats.charts.topPublishers.length > 0 ? (
                <div className="list-chart">
                  {stats.charts.topPublishers.slice(0, 6).map((item, index) => (
                    <div key={index} className="list-item">
                      <span
                        className="list-rank transition-colors duration-200"
                        style={{ color: colors.primary }}
                      >
                        #{index + 1}
                      </span>
                      <span
                        className="list-name transition-colors duration-200"
                        style={{ color: colors.text }}
                      >
                        {item.name}
                      </span>
                      <span
                        className="list-value transition-colors duration-200"
                        style={{ color: colors.textSecondary }}
                      >
                        {item.count}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <p
                  className="no-data transition-colors duration-200"
                  style={{ color: colors.textSecondary }}
                >
                  No publisher data available
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Popular Items */}
      <div className="popular-section">
        <h3 style={{ color: colors.text }}>Popular Items</h3>
        <div className="popular-grid">
          <div
            className="popular-card transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border,
              boxShadow: `0 2px 8px ${colors.shadow}`
            }}
          >
            <h4 style={{ color: colors.text }}>🏆 Most Popular Category</h4>
            <div className="popular-content">
              <div
                className="popular-name transition-colors duration-200"
                style={{ color: colors.primary }}
              >
                {stats.popular.category?.name || 'N/A'}
              </div>
              <div
                className="popular-count transition-colors duration-200"
                style={{ color: colors.textSecondary }}
              >
                {stats.popular.category?.count || 0} documents
              </div>
            </div>
          </div>
          <div
            className="popular-card transition-colors duration-200"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border,
              boxShadow: `0 2px 8px ${colors.shadow}`
            }}
          >
            <h4 style={{ color: colors.text }}>📖 Most Common Type</h4>
            <div className="popular-content">
              <div
                className="popular-name transition-colors duration-200"
                style={{ color: colors.primary }}
              >
                {stats.popular.type}
              </div>
              <div
                className="popular-count transition-colors duration-200"
                style={{ color: colors.textSecondary }}
              >
                Primary document type
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search Statistics */}
      {stats.search && (
        <div className="search-stats-section">
          <h3 style={{ color: colors.text }}>🔍 Search Statistics</h3>
          <div className="search-stats-grid">
            <div
              className="search-stat-card transition-colors duration-200"
              style={{
                backgroundColor: colors.surface,
                borderColor: colors.border,
                boxShadow: `0 2px 8px ${colors.shadow}`
              }}
            >
              <h4 style={{ color: colors.text }}>Total Indexed</h4>
              <div
                className="search-stat-value transition-colors duration-200"
                style={{ color: colors.primary }}
              >
                {stats.search.total_indexed || 0}
              </div>
            </div>
            {stats.search.by_type && Object.keys(stats.search.by_type).length > 0 && (
              <div
                className="search-stat-card transition-colors duration-200"
                style={{
                  backgroundColor: colors.surface,
                  borderColor: colors.border,
                  boxShadow: `0 2px 8px ${colors.shadow}`
                }}
              >
                <h4 style={{ color: colors.text }}>Indexed by Type</h4>
                <div className="search-type-list">
                  {Object.entries(stats.search.by_type).map(([type, count]) => (
                    <div key={type} className="search-type-item">
                      <span style={{ color: colors.text }}>{type}:</span>
                      <span style={{ color: colors.textSecondary }}>{count}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Statistics;
